import 'package:shop_chill_app/screens/my_account/models/user_model.dart';

class ProviderAuthResponse {
  bool? status;
  String? code;
  String? message;
  User? data;

  ProviderAuthResponse({this.status, this.code, this.message, this.data});

  ProviderAuthResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    code = json['code'];
    message = json['message'];
    data = json['data'] != null && json['data'] is Map<String, dynamic> ? User.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['status'] = status;
    data['code'] = code;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

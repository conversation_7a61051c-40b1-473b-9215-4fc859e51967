import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:isar/isar.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/backend/api_requests/domain.dart';
import 'package:shop_chill_app/backend/schema/local_database/local_database.dart';
import 'package:shop_chill_app/backend/schema/local_database/shopchill_database.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/forgot_password/forgot_password_request_paget.dart';
import 'package:shop_chill_app/screens/login/login_with_otp.dart';
import 'package:shop_chill_app/screens/login/widgets/dev_mode_dialog.dart';
import 'package:shop_chill_app/screens/login/widgets/phone_text_form_field.dart';
import 'package:shop_chill_app/screens/login/widgets/reusable_social_login_buttons.dart';
import 'package:shop_chill_app/screens/login/widgets/submit_button.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/notification/fcm/fcm_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/order/bloc/order_count_cubit/order_count_cubit.dart';
import 'package:shop_chill_app/screens/register/register_paget.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/dialog.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_logo.dart';
import '../flutter_flow/custom_functions.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

class LoginWidget extends StatefulWidget {
  const LoginWidget({super.key});

  @override
  _LoginWidgetState createState() => _LoginWidgetState();
}

class _LoginWidgetState extends State<LoginWidget> {
  final scaffoldKey = GlobalKey<ScaffoldState>();
  TextEditingController passwordController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  late bool passwordVisibility;

  final ShopChillDatabase localDatabase = ShopChillDatabase();

  final ValueNotifier<bool> isLoginEnabled = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    passwordVisibility = false;

    phoneController.addListener(_validateLoginInput);
    passwordController.addListener(_validateLoginInput);
  }

  @override
  void dispose() {
    phoneController.removeListener(_validateLoginInput);
    passwordController.removeListener(_validateLoginInput);
    phoneController.dispose();
    passwordController.dispose();

    super.dispose();
  }

  void _validateLoginInput() {
    final valid = phoneController.text.isNotEmpty && passwordController.text.isNotEmpty;
    if (isLoginEnabled.value != valid) {
      isLoginEnabled.value = valid;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      appBar: _buildLogoAppBar(context),
      backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
      body: GestureDetector(onTap: () => FocusScope.of(context).unfocus(), child: _buildBody(context)),
    );
  }

  Widget _headerSection(BuildContext context) {
    return Text(
      'เข้าสู่ระบบ',
      textAlign: TextAlign.center,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(color: ColorThemeConfig.primaryColor, fontWeight: FontWeight.w700),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShopchillLogo(onLongPress: () => _devMode()),
                  20.gap,
                  _headerSection(context),
                  _buildInputPhoneNumberSection(context),
                  _buildInputPasswordSection(context),
                  _buildLoginWithOTPAndForgotPasswordSection(context),
                  30.gap,
                  _buildLoginButtonSection(context),
                  _buildSocialAuthSection(context),
                  _builNewMembershipSection(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialAuthSection(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Stack(
            alignment: Alignment.center,
            children: [
              const Divider(),
              Container(
                margin: const EdgeInsets.only(bottom: 5),
                color: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: AutoSizeText(
                  'เข้าสู่ระบบด้วยวิธีอื่น',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey),
                  maxFontSize: 14,
                ),
              ),
            ],
          ),
        ),
        const ReusableSocialLoginButtons(),
      ],
    );
  }

  AppBar _buildLogoAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.white,
      automaticallyImplyLeading: true,
      leading: IconButton(
        onPressed: () => Navigator.pop(context, false),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
    );
  }

  Widget _builNewMembershipSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 50, 0, 30),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('คุณไม่มีบัญชี?', style: Theme.of(context).textTheme.labelMedium?.copyWith(color: Colors.grey)),

          5.gap,
          GestureDetector(
            onTap: () async {
              await Navigator.push(context, CupertinoPageRoute(builder: (context) => const RegisterPage()));
            },
            child: Text('สร้างบัญชีใหม่', style: Theme.of(context).textTheme.labelMedium?.copyWith(color: ColorThemeConfig.primaryColor)),
          ),
        ],
      ),
    );
  }

  Padding _buildLoginWithOTPAndForgotPasswordSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 10, 0, 0),
      child: Align(
        alignment: const AlignmentDirectional(1, 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            InkWell(
              onTap: () async {
                await Navigator.push(context, CupertinoPageRoute(builder: (context) => const LoginWithOTP()));
              },
              child: Text('เข้าสู่ระบบด้วย OTP', style: FlutterFlowTheme.of(context).bodyText2.copyWith(color: ColorThemeConfig.textButtonColor)),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Container(color: Colors.grey.shade300, width: .8, height: 18),
            ),
            InkWell(
              onTap: () async {
                await Navigator.push(context, CupertinoPageRoute(builder: (context) => const ForgotPasswordRequestPage()));
              },
              child: Text('ลืมรหัสผ่าน?', style: FlutterFlowTheme.of(context).bodyText2.copyWith(color: ColorThemeConfig.textButtonColor)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputPhoneNumberSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: PhoneTextFormField(phoneController: phoneController),
    );
  }

  Widget _buildInputPasswordSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: TextFormField(
        controller: passwordController,
        obscureText: !passwordVisibility,
        decoration: InputDecoration(
          labelText: 'รหัสผ่าน',
          labelStyle: Theme.of(context).textTheme.labelMedium?.copyWith(color: ColorThemeConfig.primaryColor, fontWeight: FontWeight.w700),
          prefixIcon: const Icon(Icons.lock, size: 24, color: ColorThemeConfig.primaryColor),
          hintStyle: FlutterFlowTheme.of(context).bodyText2,
          enabledBorder: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.circular(8)),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: ColorThemeConfig.primaryColor, width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          floatingLabelBehavior: FloatingLabelBehavior.auto,
          filled: true,
          fillColor: ColorThemeConfig.fillColor,
          contentPadding: const EdgeInsets.all(10),
          suffixIcon: InkWell(
            borderRadius: BorderRadius.circular(20),
            onTap: () => setState(() => passwordVisibility = !passwordVisibility),
            focusNode: FocusNode(skipTraversal: true),
            child: Icon(passwordVisibility ? Icons.visibility : Icons.visibility_off, color: ColorThemeConfig.grayIcon2, size: 22),
          ),
        ),
        style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black),
      ),
    );
  }

  Widget _buildLoginButtonSection(BuildContext context) {
    return SubmitButton(
      isEnabled: isLoginEnabled,
      onTap: () async {
        FocusScope.of(context).unfocus();
        await _onLoginProcess();
      },
      label: 'เข้าสู่ระบบ',
    );
  }

  Future<void> _onLoginProcess() async {
    if (!validateMobile(phoneController.text, context)) {
      return;
    } else if (!validatePassword(passwordController.text, context)) {
      return;
    }
    showLoading();

    final Isar db = await localDatabase.openDB();

    final List<PhoneProfile> phoneProfiles = await db.phoneProfiles.where().findAll();

    final isExist = phoneProfiles.where((element) => element.uid == phoneController.text).isNotEmpty;
    hideLoading();
    if (isExist) {
      ShopChillDialogs().showReactivationAccountDialog(
        context,
        callback: () async {
          final Isar db = await localDatabase.openDB();

          await db.writeTxn(() async {
            final success = await db.phoneProfiles.filter().uidEqualTo(phoneController.text).deleteFirst();
            print('Recipe deleted: $success');
          });

          _handleLoginResponse();
        },
      );
    } else {
      _handleLoginResponse();
    }
  }

  Future<void> _handleLoginResponse() async {
    final String? fcmToken = await getFcmToken();

    final loginResponse = await LoginCall.call(phone: phoneController.text, password: passwordController.text, fcmToken: fcmToken);
    final status = LoginCall.status((loginResponse.jsonBody ?? ''));
    final token = LoginCall.token((loginResponse.jsonBody ?? '')).toString();
    final msg = LoginCall.message((loginResponse.jsonBody ?? ''));
    if (status == true && token.isNotEmpty) {
      FFAppState().token = LoginCall.token((loginResponse.jsonBody ?? '')).toString();
    } else {
      showError(msg);
      return;
    }

    context.read<UserBloc>().add(const GetUserProfile());
    context.read<CartBloc>().add(GetCartEvent());
    context.read<OrderCountCubit>().fetchOrderCount();

    Navigator.of(context).popUntil((route) => route.isFirst);
    return;
  }

  void _devMode() async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) {
        final TextEditingController passwordController = TextEditingController();
        return DevModeDialog(passwordController: passwordController);
      },
    );

    // ตรวจสอบรหัสผ่าน
    if (result != null && result == 'shopchill_dev123') {
      await showModalBottomSheet(
        context: context,
        builder: (_) => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            for (final env in ['dev', 'production'])
              ListTile(
                title: Text('Switch to $env'),
                onTap: () async {
                  await domain.setEnv(env);
                  Navigator.pop(context);

                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('ENV changed to $env')));

                  print("ENV changed to $env");
                  Phoenix.rebirth(context);
                },
              ),
          ],
        ),
      );
    } else if (result != null) {
      // ใส่รหัสผิด
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Incorrect password')));
    }
  }
}

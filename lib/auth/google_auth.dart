import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';

import '../app_state.dart';
import 'auth_util.dart';

final _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

Future<User?> signInWithGoogle(BuildContext context) async {
  showLoading();
  try {
    Future<UserCredential?> signInFunc() async {
      try {
        print('🔵 Starting Google Sign-In...');

        // Check if Google Play Services is available
        print('🔵 Checking Google Play Services availability...');
        await _googleSignIn.isSignedIn();
        print('🔵 Google Services check completed');

        await signOutWithGoogle().catchError((_) => null);

        print('🔵 Attempting Google Sign-In...');
        final res = await _googleSignIn.signIn();

        if (res == null) {
          print('🔵 ❌ Google Sign-In was cancelled by user');
          hideLoading();
          return null;
        }

        print('🔵 ✅ Google Sign-In successful, getting authentication...');
        final auth = await res.authentication;

        if (auth.idToken == null || auth.accessToken == null) {
          print('🔵 ❌ Missing tokens - idToken: ${auth.idToken != null}, accessToken: ${auth.accessToken != null}');
          hideLoading();
          return null;
        }

        print('🔵 ✅ Got Google tokens, creating Firebase credential...');
        final credential = GoogleAuthProvider.credential(idToken: auth.idToken, accessToken: auth.accessToken);

        print("🔵 Firebase credential created: $credential");
        FFAppState().profileId = res.id;
        FFAppState().oauthToken = auth.idToken!;
        FFAppState().googleAccessToken = auth.accessToken!;

        print('🔵 ✅ Signing in with Firebase...');
        final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);
        print('🔵 ✅ Firebase sign-in successful');

        hideLoading();
        return userCredential;
      } catch (e) {
        hideLoading();
        print("🔵 ❌ Error in Google sign in: $e");

        // Handle specific error codes
        if (e.toString().contains('12500')) {
          print('🔵 ❌ Error 12500: Sign-in cancelled or configuration issue');
          throw Exception('Google sign-in was cancelled or there\'s a configuration issue. Please try again.');
        } else if (e.toString().contains('7')) {
          print('🔵 ❌ Error 7: Network error');
          throw Exception('Network error during Google sign-in. Please check your connection.');
        } else if (e.toString().contains('10')) {
          print('🔵 ❌ Error 10: Developer error - check SHA-1');
          throw Exception('Google sign-in configuration error. Please contact support.');
        }

        throw Exception('Google sign-in failed: $e');
      }
    }

    return signInOrCreateAccount(context, signInFunc);
  } catch (e) {
    hideLoading();
    print('🔵 ❌ Outer catch - Google sign in failed: $e');
    throw Exception('Google sign in failed: $e');
  } finally {
    hideLoading();
  }
}

Future signOutWithGoogle() => _googleSignIn.signOut();

/* import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_icon_button.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/nav_bar/nav_bar_page.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';

import '../../config/shopchill_loading/shopchill_loading.dart';

class RegisterFullWidget extends StatefulWidget {
  final String phone;
  const RegisterFullWidget({super.key, required this.phone});

  @override
  State<RegisterFullWidget> createState() => _RegisterFullWidgetState();
}

class _RegisterFullWidgetState extends State<RegisterFullWidget> {
  final formKey = GlobalKey<FormState>();
  TextEditingController confirmPasswordController = TextEditingController();
  late bool confirmPasswordVisibility;
  TextEditingController emailAddressController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController userNameController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  late bool passwordVisibility;
  @override
  void initState() {
    phoneController.text = widget.phone;
    confirmPasswordVisibility = false;
    passwordVisibility = false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        body: SingleChildScrollView(
          child: Form(
            autovalidateMode: AutovalidateMode.onUserInteraction,
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 50, 0, 16),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            // 'assets/images/logo_horizontal.png',
                            UImageAssets.HORIZONTAL_LOGO,
                            width: 186,
                            height: 64,
                            fit: BoxFit.fitWidth,
                          ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(0, 0),
                      child: Stack(
                        alignment: const AlignmentDirectional(0, 0),
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(-1, 0),
                            child: FlutterFlowIconButton(
                              borderColor: Colors.transparent,
                              borderRadius: 30,
                              borderWidth: 1,
                              buttonSize: 60,
                              icon: const FaIcon(FontAwesomeIcons.angleLeft, color: Colors.black, size: 20),
                              onPressed: () async {
                                Navigator.pop(context);
                              },
                            ),
                          ),
                          Align(
                            alignment: const AlignmentDirectional(0, 0),
                            child: Text(
                              'สมัครสมาชิกใหม่',
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(
                                context,
                              ).title1.override(fontFamily: 'Sarabun', color: Colors.black, fontSize: 26, fontWeight: FontWeight.w700, useGoogleFonts: false),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'กรอกชื่อผู้ใช้งาน',
                                        style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                      ),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                        child: Container(
                                          width: double.infinity,
                                          decoration: const BoxDecoration(),
                                          child: TextFormField(
                                            validator: (value) {
                                              if (value!.isEmpty) {
                                                return 'กรุณากรอก username';
                                              } else {
                                                return null;
                                              }
                                            },
                                            controller: userNameController,
                                            obscureText: false,
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                              labelText: 'username',
                                              floatingLabelBehavior: FloatingLabelBehavior.never,
                                              hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              filled: true,
                                              fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                                              contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                            ),
                                            style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'ชื่อ-นามสกุล',
                                        style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                      ),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                        child: Container(
                                          width: double.infinity,
                                          decoration: const BoxDecoration(),
                                          child: TextFormField(
                                            validator: (value) {
                                              if (value!.isEmpty) {
                                                return 'กรุณากรอก ชื่อ-นามสกุล';
                                              } else {
                                                return null;
                                              }
                                            },
                                            controller: nameController,
                                            obscureText: false,
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                              labelText: 'ชื่อ นามสกุล',
                                              floatingLabelBehavior: FloatingLabelBehavior.never,
                                              hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              filled: true,
                                              fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                                              contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                            ),
                                            style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'หมายเลขโทรศัพท์',
                                        style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                      ),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                        child: Container(
                                          width: double.infinity,
                                          decoration: const BoxDecoration(),
                                          child: TextFormField(
                                            readOnly: true,
                                            onChanged: (value) {
                                              setState(() {});
                                            },
                                            validator: (value) {
                                              if (value!.isEmpty) {
                                                return 'กรุณากรอกเบอร์โทร';
                                              } else {
                                                return null;
                                              }
                                            },
                                            controller: phoneController,
                                            obscureText: false,
                                            maxLength: 10,
                                            decoration: InputDecoration(
                                              counterText: '',
                                              labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                              labelText: '081xxxxxxx',
                                              floatingLabelBehavior: FloatingLabelBehavior.never,
                                              hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              filled: true,
                                              fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                                              contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                            ),
                                            style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false),
                                            keyboardType: TextInputType.phone,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),

                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'E-mail',
                                        style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                      ),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                        child: Container(
                                          width: double.infinity,
                                          decoration: const BoxDecoration(),
                                          child: TextFormField(
                                            validator: (value) {
                                              RegExp emailRegex = RegExp(r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$");

                                              if (value == null || value.isEmpty) {
                                                return 'กรุณากรอก e-mail';
                                              } else if (!emailRegex.hasMatch(value)) {
                                                return 'รูปแบบ e-mail ไม่ถูกต้อง';
                                              } else {
                                                return null;
                                              }
                                            },
                                            controller: emailAddressController,
                                            obscureText: false,
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                              labelText: '<EMAIL>',
                                              floatingLabelBehavior: FloatingLabelBehavior.never,
                                              hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              filled: true,
                                              fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                                              contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                            ),
                                            style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false),
                                            keyboardType: TextInputType.emailAddress,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'รหัสผ่าน',
                                        style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                      ),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                        child: Container(
                                          width: double.infinity,
                                          decoration: const BoxDecoration(),
                                          child: TextFormField(
                                            validator: (value) {
                                              if (value == null || value.isEmpty) {
                                                return 'กรุณากรอกรหัสผ่าน';
                                              } else if (value.length < 8) {
                                                return 'รหัสผ่านต้องมีความยาวอย่างน้อย 8 ตัว';
                                              } else {
                                                return null;
                                              }
                                            },
                                            controller: passwordController,
                                            obscureText: !passwordVisibility,
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                              labelText: '**********',
                                              floatingLabelBehavior: FloatingLabelBehavior.never,
                                              hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              filled: true,
                                              fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                                              contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                              suffixIcon: InkWell(
                                                onTap: () => setState(() => passwordVisibility = !passwordVisibility),
                                                focusNode: FocusNode(skipTraversal: true),
                                                child: Icon(
                                                  passwordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                                                  color: const Color(0xFF757575),
                                                  size: 22,
                                                ),
                                              ),
                                            ),
                                            style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'ยืนยันรหัสผ่าน',
                                        style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                      ),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                        child: Container(
                                          width: double.infinity,
                                          decoration: const BoxDecoration(),
                                          child: TextFormField(
                                            validator: (value) {
                                              if (value == null || value.isEmpty) {
                                                return 'กรุณากรอกยืนยันรหัสผ่าน';
                                              } else if (value != passwordController.text) {
                                                return 'ยืนยันรหัสผ่านไม่ตรงกับรหัสผ่านที่คุณป้อน';
                                              } else {
                                                return null;
                                              }
                                            },
                                            controller: confirmPasswordController,
                                            obscureText: !confirmPasswordVisibility,
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                              labelText: '**********',
                                              floatingLabelBehavior: FloatingLabelBehavior.never,
                                              hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              filled: true,
                                              fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                                              contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                              suffixIcon: InkWell(
                                                onTap: () => setState(() => confirmPasswordVisibility = !confirmPasswordVisibility),
                                                focusNode: FocusNode(skipTraversal: true),
                                                child: Icon(
                                                  confirmPasswordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                                                  color: const Color(0xFF757575),
                                                  size: 22,
                                                ),
                                              ),
                                            ),
                                            style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Padding(
                          //   padding: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 8),
                          //   child: ClipRRect(
                          //     borderRadius: BorderRadius.circular(4),
                          //     child: InkWell(
                          //       onTap: () async {
                          //         // setState(() {
                          //         //   checkboxListTileValue = !checkboxListTileValue!;
                          //         // });
                          //       },
                          //       splashColor: Colors.transparent,
                          //       highlightColor: Colors.transparent,
                          //       child: Row(
                          //         children: [
                          //           if (checkboxListTileValue!) ...[
                          //             Container(
                          //               width: 24,
                          //               height: 24,
                          //               decoration: BoxDecoration(
                          //                 gradient: LinearGradient(
                          //                   colors: [
                          //                     Color(0xFF0084F0),
                          //                     Color(0xFF0073E6),
                          //                     Color(0xFF0056D6),
                          //                   ],
                          //                   begin: AlignmentDirectional(0, -1),
                          //                   end: AlignmentDirectional(0, 1),
                          //                 ),
                          //                 shape: BoxShape.rectangle,
                          //                 borderRadius: BorderRadius.circular(4),
                          //               ),
                          //               alignment: AlignmentDirectional(0, 0),
                          //               child: FaIcon(
                          //                 FontAwesomeIcons.check,
                          //                 color: FlutterFlowTheme.of(context).secondaryBackground,
                          //                 size: 12,
                          //               ),
                          //             ),
                          //           ] else ...[
                          //             Container(
                          //               width: 24,
                          //               height: 24,
                          //               decoration: BoxDecoration(
                          //                 border: Border.all(color: Colors.grey),
                          //                 shape: BoxShape.rectangle,
                          //                 borderRadius: BorderRadius.circular(4),
                          //               ),
                          //               alignment: AlignmentDirectional(0, 0),
                          //               child: FaIcon(
                          //                 FontAwesomeIcons.check,
                          //                 color: FlutterFlowTheme.of(context).secondaryBackground,
                          //                 size: 12,
                          //               ),
                          //             ),
                          //           ],
                          //           Text(
                          //             '    ยอมรับเงื่อนไขและข้อกำหนด',
                          //             style: FlutterFlowTheme.of(context).title3.override(
                          //                   fontFamily: 'Sarabun',
                          //                   color: Colors.black,
                          //                   fontSize: 16,
                          //                   fontWeight: FontWeight.w400,
                          //                   useGoogleFonts: false,
                          //                 ),
                          //           ),
                          //         ],
                          //       ),
                          //     ),
                          //   ),
                          // ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                            child: InkWell(
                              onTap: () => _onSubmitRegister(),
                              child: Container(
                                width: double.infinity,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context).secondaryBackground,
                                  image: DecorationImage(
                                    fit: BoxFit.cover,
                                    image: Image.asset(
                                      // 'assets/images/button_bg.png',
                                      UImageAssets.BUTTON_BG,
                                    ).image,
                                  ),
                                  borderRadius: BorderRadius.circular(14),
                                ),
                                child: Align(
                                  alignment: const AlignmentDirectional(0, 0),
                                  child: Text(
                                    'สมัครสมาชิก',
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context).title2.override(fontFamily: 'Sarabun', color: Colors.white, useGoogleFonts: false),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onSubmitRegister() async {
    if (formKey.currentState!.validate()) {
      ShopChillLoading.show();
      try {
        bool shouldSetState = false;
        final res = await RegisterCall().register(
          name: nameController.text,
          username: userNameController.text,
          email: emailAddressController.text,
          phone: phoneController.text,
          password: passwordController.text,
          confirmPassword: confirmPasswordController.text,
        );
        shouldSetState = true;
        /*  print('value = $res');
        if (res['status'] == true) {
          ShopChillLoading.showSuccess(res['msg']);

          Navigator.pop(context);
        } else {
          ShopChillLoading.showError(res['msg']);
        } */
        if (res['success'] ?? false) {
          setState(() => FFAppState().token = res['data']['api_token']);
          context.read<UserBloc>().add(GetUserProfile());
          // Navigator.pop(context, true);
          // Navigator.pop(context, true);
          ShopChillLoading.dismiss();

          await Navigator.pushAndRemoveUntil(context, CupertinoPageRoute(builder: (context) => const NavBarPage(initialPage: 4)), (r) => false);
          if (shouldSetState) setState(() {});
          return;
        } else {
          showError(res['msg'] ?? 'เกิดข้อผิดพลาด!');
          if (shouldSetState) setState(() {});
          return;
        }
      } finally {
        ShopChillLoading.dismiss();
      }
    }
  }
}
 */
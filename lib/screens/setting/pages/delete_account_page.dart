import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:pinput/pinput.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/auth/apple_auth.dart';
import 'package:shop_chill_app/backend/repository/centtal_repository.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_icon_button.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/setting/pages/delete_detail_page.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';
import 'package:shop_chill_app/shered/util/custom_modal.dart';
import 'package:shop_chill_app/shered/util/custom_text.dart';

import '../../../config/shopchill_loading/shopchill_loading.dart';

final _googleSignIn = GoogleSignIn();

class DeleteAccountPage extends StatefulWidget {
  final String profile;
  final String name;

  const DeleteAccountPage({super.key, required this.profile, required this.name});

  @override
  State<DeleteAccountPage> createState() => _DeleteAccountPageState();
}

class _DeleteAccountPageState extends State<DeleteAccountPage> {
  final TextEditingController _pinController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    final currentUid = user?.uid ?? '';
    final providerId = user?.providerData.first.providerId ?? '';
    final platform = CheckProviderType.checkProviderType(providerId);
    final platformLoginName = CheckProviderType.ProviderTypeName(platform);
    print('platform: $platform');
    print('platformLoginName: $platformLoginName');

    return Scaffold(
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
          automaticallyImplyLeading: false,
          leading: FlutterFlowIconButton(
            borderColor: Colors.transparent,
            borderRadius: 30,
            borderWidth: 1,
            buttonSize: 48,
            icon: Icon(
              Icons.arrow_back_ios,
              color: FlutterFlowTheme.of(context).black600,
              size: 24,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          centerTitle: true,
          elevation: 0,
        ),
        body: BlocBuilder<UserBloc, UserState>(
          builder: (context, state) {
            if (state is UserLoaded) {
              final user = state.userModel!.data!.user!;
              return Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                    child: CustomText(
                      text: 'ยืนยันบัญชี $platformLoginName',
                      color: FlutterFlowTheme.of(context).black600,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),

                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                    child: CustomText(
                      text: 'ก่อนลบบัญชีของคุณ คุณต้องยืนยันบัญชี $platformLoginName ที่เชื่อมโยงกับบัญชี ShopChill ของคุณ',
                      color: FlutterFlowTheme.of(context).black600,
                      fontWeight: FontWeight.normal,
                      fontSize: 16,
                      // textAlign: TextAlign.left,
                    ),
                  ),

                  const SizedBox(
                    height: 30,
                  ),

                  CircleAvatar(
                    radius: 50,
                    backgroundImage: NetworkImage(widget.profile),
                  ),
                  CustomText(
                    text: user.name!,
                    color: FlutterFlowTheme.of(context).black600,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: CupertinoButton(
                      color: FlutterFlowTheme.of(context).primaryColor,
                      child: CustomText(
                        text: 'ยืนยันบัญชี $platformLoginName',
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                      onPressed: () {
                        deleteAccount(context, platform);
                      },
                    ),
                  ),
                  // Image.network(user.!.data!.url!),

                  // Image.network(user.!.data!.url!),
                ],
              );
            } else {
              return const SizedBox();
            }
          },
        ));
  }

  deleteAccount(BuildContext context, ProviderType platform) async {
    switch (platform) {
      case ProviderType.facebook:
        await deleteFacebookAccount(context);
        break;
      case ProviderType.google:
        await deleteGoogleAccount(context);
        break;
      case ProviderType.apple:
        deleteAppleAccount(context);
        break;
      case ProviderType.others:
        print('others');
        deletePhoneAccount(context);
        break;
      default:
        break;
    }
  }

  /*Navigator.push(
  context,
  CupertinoPageRoute(
  builder: (context) => DeleteDetailPage(
  uid: currentUid,
  name: widget.name,
  profile: widget.profile,
  email: user.email!,
  platform: CheckProviderType.checkProviderType(user.providerData.first.providerId),
  ),
  ),
  );*/

  deletePhoneAccount(BuildContext context) {
    CustomModal().showModal(
        context: context,
        child: BlocBuilder<UserBloc, UserState>(
          builder: (context, state) {
            if (state is UserLoaded) {
              final user = state.userModel!.data!.user!;
              return Column(
                children: [
                  CustomText(
                    text: 'ยืนยันหมายเลขโทรศัพท์',
                    color: FlutterFlowTheme.of(context).black600,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                  const SizedBox(height: 10),
                  CustomText(
                    text: 'เพื่อลบบัญชีของคุณ กรุณายืนยันหมายเลขโทรศัพท์ที่เชื่อมโยงกับบัญชี ShopChill ของคุณ',
                    color: FlutterFlowTheme.of(context).black600,
                    fontWeight: FontWeight.normal,
                    fontSize: 16,
                  ),
                  const SizedBox(height: 30),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          // width: MediaQuery.of(context).size.width * 0.9,
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: CustomText(text: '${user.phone}', fontSize: 20, color: Colors.black, fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(width: 10),
                      CupertinoButton(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                        color: FlutterFlowTheme.of(context).primaryColor,
                        onPressed: () {
                          CenTrailRepository().sendSMSOTP(phoneNumber: user.phone!).whenComplete(() => ShopChillLoading.showToast(
                                'OTP ถูกส่งไปที่ ${user.phone}',
                              ));
                        },
                        child: const Row(
                          children: [
                            CustomText(
                              text: 'ขอรหัส OTP',
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                            SizedBox(width: 10),
                            Icon(
                              Icons.send,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 30),
                  Padding(
                    padding: const EdgeInsetsDirectional.all(8),
                    child: animatedBorders(user),
                  ),
                  const SizedBox(height: 10),
                  const SizedBox(height: 10),
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: CupertinoButton(
                      color: FlutterFlowTheme.of(context).primaryColor,
                      child: const CustomText(
                        text: 'ยืนยันหมายเลขโทรศัพท์',
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                      onPressed: () {
                        CenTrailRepository().verifySMSOTP(phoneNumber: user.phone!, smsOTP: _pinController.text).then((value) {
                          if (value) {
                            Navigator.push(
                              context,
                              CupertinoPageRoute(
                                builder: (context) => DeleteDetailPage(
                                  uid: user.id!,
                                  name: widget.name,
                                  profile: widget.profile,
                                  email: user.email!,
                                  platform: CheckProviderType.checkProviderType(''),
                                ),
                              ),
                            );
                          } else {
                            ShopChillLoading.showError('รหัส OTP ไม่ถูกต้อง');
                          }
                        });
                      },
                    ),
                  ),
                ],
              );
            } else {
              return const SizedBox();
            }
          },
        ));
  }

  Widget animatedBorders(user) {
    const length = 6;
    const borderColor = Color.fromRGBO(114, 178, 238, 1);
    const errorColor = Color.fromRGBO(255, 234, 238, 1);
    const fillColor = Color.fromRGBO(222, 231, 240, .57);
    final defaultPinTheme = PinTheme(
      width: 60,
      height: 60,
      textStyle: GoogleFonts.poppins(
        fontSize: 22,
        color: const Color.fromRGBO(30, 60, 87, 1),
      ),
      decoration: BoxDecoration(
        color: fillColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.transparent),
      ),
    );

    return SizedBox(
      height: 60,
      child: Pinput(
        length: length,
        controller: _pinController,
        // focusNode: focusNode,
        defaultPinTheme: defaultPinTheme,
        onCompleted: (pin) async {
          CenTrailRepository().verifySMSOTP(phoneNumber: user.phone!, smsOTP: _pinController.text).then((value) {
            if (value) {
              Navigator.push(
                context,
                CupertinoPageRoute(
                  builder: (context) => DeleteDetailPage(
                    uid: user.phone.toString(),
                    name: widget.name,
                    profile: widget.profile,
                    email: user.email!,
                    platform: CheckProviderType.checkProviderType(''),
                  ),
                ),
              );
            } else {
              ShopChillLoading.showError('รหัส OTP ไม่ถูกต้อง');
            }
          });
        },
        focusedPinTheme: defaultPinTheme.copyWith(
          height: 60,
          width: 60,
          decoration: defaultPinTheme.decoration!.copyWith(
            border: Border.all(color: borderColor),
          ),
        ),
        errorPinTheme: defaultPinTheme.copyWith(
          decoration: BoxDecoration(
            color: errorColor,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Future<void> deleteAppleAccount(BuildContext context) async {
    final users = FirebaseAuth.instance.currentUser!;
    final currentUid = users.uid;
    final user = await signInWithApple(context);
    if (user != null) {
      if (user.uid != currentUid) {
        print('Error: reauthenticateWithCredential failed');
      } else {
        // await user.delete();
        Navigator.push(
          context,
          CupertinoPageRoute(
            builder: (context) => DeleteDetailPage(
              uid: currentUid,
              name: widget.name,
              profile: widget.profile,
              email: user.email!,
              platform: CheckProviderType.checkProviderType(user.providerData.first.providerId),
            ),
          ),
        );
      }
    } else {
      ShopChillLoading.showError('การตรวจสอบล้มเหลว');
    }
  }

  Future<void> deleteGoogleAccount(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser!;
    final currentEmail = user.email;
    final currentUid = user.uid;

    try {
      // ลงชื่อออกจาก Google
      await _googleSignIn.signOut();
      // ลงชื่อเข้าใช้ใหม่ด้วย Google
      var res = await _googleSignIn.signIn();
      final auth = await res?.authentication;
      // ตรวจสอบว่าอีเมลตรงกับบัญชีปัจจุบันหรือไม่
      if (res?.email == currentEmail) {
        if (auth != null) {
          // ใช้ข้อมูลประจำตัวจาก Google เพื่อทำการ re-authenticate
          final credential = GoogleAuthProvider.credential(
            idToken: auth.idToken,
            accessToken: auth.accessToken,
          );
          final reOauth = await user.reauthenticateWithCredential(credential);

          print('reOauth: ${reOauth.user!.uid} - $currentUid');
          if (reOauth.user!.uid != currentUid) {
            print('Error: reauthenticateWithCredential failed');
          } else {
            // ย้ายไปหน้า DeleteDetailPage หลังจากการยืนยันสำเร็จ
            Navigator.push(
              context,
              CupertinoPageRoute(
                builder: (context) => DeleteDetailPage(
                  uid: currentUid,
                  name: widget.name,
                  profile: widget.profile,
                  email: user.email!,
                  platform: CheckProviderType.checkProviderType(user.providerData.first.providerId),
                ),
              ),
            );
          }
        } else {
          ShopChillLoading.showError('การตรวจสอบล้มเหลว');
        }
      } else {
        ShopChillLoading.showError('อีเมลไม่ตรงกัน');
      }
    } catch (e) {
      print(e);
    }
  }

  /* Future<void> deleteGoogleAccount(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser!;
    final currentEmail = user.email;
    final currentUid = user.uid;
    try {
      await _googleSignIn.signOut();
      var res = await _googleSignIn.signIn();
      final auth1 = await res?.authHeaders;
      final auth = await res?.authentication;
      print("res email => ${res?.email}");
      print("res id => ${auth?.idToken}");

      print("currentEmail  => ${currentEmail}");
      print("currentUid  => ${currentUid}");
      print("is 1 => ${res?.email == currentEmail}");
      print("is 2 => ${res?.id == currentUid}");
      if (auth != null && auth.idToken == currentUid) {
        final credential = GoogleAuthProvider.credential(idToken: auth.idToken, accessToken: auth.accessToken);
        final reOauth = await user.reauthenticateWithCredential(credential);
        print('reOauth: ${reOauth.user!.uid} - $currentUid');
        if (reOauth.user!.uid != currentUid) {
          print('Error: reauthenticateWithCredential failed');
        } else {
          // await user.delete();
          Navigator.push(
            context,
            CupertinoPageRoute(
              builder: (context) => DeleteDetailPage(
                uid: currentUid ?? "",
                name: widget.name,
                profile: widget.profile,
                email: user.email!,
                platform: CheckProviderType.checkProviderType(user.providerData.first.providerId),
              ),
            ),
          );
        }
      } else {
        ShopChillLoading.showError('การตรวจสอบล้มเหลว');
      }
    } catch (e) {
      print(e);
    }
  } */

  Future<void> deleteFacebookAccount(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser!;
    final currentUid = user.uid;
    final LoginResult loginResult = await FacebookAuth.instance.login(permissions: ["email"]);
    if (loginResult.status == LoginStatus.success) {
      final OAuthCredential credential = FacebookAuthProvider.credential(FFAppState().oauthToken);
      final reOauth = await user.reauthenticateWithCredential(credential);
      if (reOauth.user!.uid != currentUid) {
        print('Error: reauthenticateWithCredential failed');
      } else {
        // await user.delete();
        Navigator.push(
          context,
          CupertinoPageRoute(
            builder: (context) => DeleteDetailPage(
              uid: currentUid,
              name: widget.name,
              profile: widget.profile,
              email: user.email!,
              platform: CheckProviderType.checkProviderType(user.providerData.first.providerId),
            ),
          ),
        );
      }
    } else {
      print('Facebook login failed: ${loginResult.status}');
      ShopChillLoading.showError('การตรวจสอบล้มเหลว');
    }
  }
}

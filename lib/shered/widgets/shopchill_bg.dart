import 'package:flutter/cupertino.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';

class ShopchillBg extends StatelessWidget {
  final Widget child;
  const ShopchillBg({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(UImageAssets.SHOPCHILL_BG, fit: BoxFit.cover, width: MediaQuery.of(context).size.width),
        SafeArea(
          child: Padding(padding: const EdgeInsets.all(16.0), child: child),
        ),
      ],
    );
  }
}

// ignore_for_file: constant_identifier_names

class ApiEndPointV2 {
  static String get GET_IMAGE_PATH => 'https://app.iship.cloud/api/file/uploader';

  static String get LOGIN => '/api/app/login';
  static String get LOGIN_FACEBOOK => '/api/app/login/facebook';
  static String get LOGIN_GOOGLE => '/api/app/login/google';
  static String get LOGIN_APPLE => '/api/app/login/apple';
  static String get LOGIN_LINE => '/api/app/login/line';
  static String get LOGIN_OTP => '/api/app/verify-sms';
  static String get REGISTER => '/api/app/register';
  static String get REGISTER_SMS => '/api/app/register-sms';
  static String get REGISTER_SMS_V2 => '/api/app/register-sms';
  static String get RE_PASSWORD => '/api/app/reset-password';
  static String get VERIFY_RESET_PASSWORD => '/api/app/verify-reset-password';
  static String get RESET_NEW_PASSWORD => '/api/app/reset-new-password';
  static String get DELETE_ACCOUNT => '/api/app/delete-user/user';
  static String get PROVIDER_AUTH => '/api/v1/auth/{provider}';

  static String get HOME_SLIDES => '/api/app/home/<USER>';
  static String get SLIDES => '/api/app/home/<USER>';

  static String get KEP_CODE_COUPON_SHIPPING => '/api/app/save-coupon';

  static String get GET_USER => '/api/user';
  static String get UPDATE_USER => '/api/app/user';
  static String get GET_MY_ADDRESS => '/api/app/user/address';
  static String get UPDATE_MY_ADDRESS => '/api/app/user/address';
  static String get DELETE_MY_ADDRESS_ID => '/api/app/user/delete-address/id';
  static String get GET_MY_REVIEW => '/api/app/user/review';
  static String get GET_MY_VOUCER => '/api/app/user/coupon';
  static String get RESET_PASSWORD => '/api/app/user/re-password';

  static String get GET_PRODUCT => '/api/product?limit=limitdata&offset=offsetdata&type=typedata';
  static String get GET_PRODUCT_NEW => '/api/product';
  static String get GET_BANNER => '/api/app/home/<USER>';
  static String get GET_FLASH_SALE => '/api/product/flashsale/list?time=param1&text=param2&shop_id=param3&limit=param4';
  static String get GET_FLASH_SALE_V2 => '/api/product/flashsale/list';
  static String get ADD_TO_CART => '/api/app/cart';
  static String get CALCULATE_PRICE => '/api/cart/calculate';
  static String get REMOVE_PRODUCT => '/api/app/remove-cart';
  static String get CLEAR_CART => '/api/app/clear-cart';
  static String get UPDATE_PRODUCT_QTY => '/api/cart/update';
  static String get GET_COUPON_SHIPPING_FREE => '/api/app/home/<USER>';

  static String get GET_NOTIFICATION => '/api/app/notification?limit={limit}&offset={offset}';

  static String get GET_PRODUCT_BY_SLUG => '/api/v2/product/';
  static String get ADD_TO_CARTS => '/api/cart';

  static String get GET_SHOP => '/api/app/shop/shopId?user_id=userId';
  static String get GET_SHOP_PRODUCT => '/api/shop/shopId/products?limit=100';
  static String get FOLLOW_SHOP => '/api/app/follow';

  static String get GET_CATEGORY => '/api/app/home/<USER>';

  static String get GET_ORDER => '/api/app/user/order?limit=rlimit&offset=roffset&status=rstatus';
  static String get GET_ORDER_DETAIL => '/api/app/user/order/{masterOrderId}';

  static String get GET_PRODUCT_REVIEW => '/api/app/review-by-orderid';

  static String get GET_DEAL_100 => '/api/app/home/<USER>';
  static String get UPLOAD_IMAGE => '/api/app/upload';

  static String get GET_HOT_DEAL => '/api/app/home/<USER>';

  static String get REMOVE_ADDRESS_ID => '/api/app/user/delete-address/addressId';
  static String get GET_HOT_DEAL_DETAIL => '/api/app/home/<USER>/id';
  static String get GET_PRODUCT_CATEGORY_BY_ID => '/api/product?limit=20&offset=0&category_id=parameter';

  static String get FIND_PRODUCT_VOUCHER => '/api/app/product-coupon/id';
  static String get GET_PAYMENT => '/api/app/payment?payment_id=paymentid';
  static String get GET_PAYMENT_V2 => '/api/payment/pay/{paymentType}?app_api=1&payment_id=paymentid';
  static String get MOBILE_BANKING => '/api/payment/pay/{paymentType}';

  static String get CONNECT_TO_GOOGLE => '/api/app/connect/google';
  static String get DISCONNECT_TO_GOOGLE => '/api/app/delete-user/google';
  static String get CONNECT_TO_FACEBOOK => '/api/app/connect/facebook';
  static String get DISCONNECT_TO_FACEBOOK => '/api/app/delete-user/facebook';

  static String get CONNECT_TO_APPLE => '/api/app/connect/apple';
  static String get DISCONNECT_TO_APPLE => '/api/app/delete-user/apple';

  static String get GET_NOTIFICATION_SETTING => '/api/setting/notice?user_type=user&user_id={userId}';
  static String get NOTIFICATION_SETTING => '/api/setting/notice';

  static String get PROVINCES => '/api/thailand/province';
  static String get DISTRICTS => '/api/thailand/district';
  static String get SUB_DISTRICTS => '/api/thailand/sub-district';
  static String get ZIPCODE => '/api/thailand/zipcode';
  //-> get-thiailand
  static String get PROVINCES_V1 => '/api/v1/datas/thailand/province';
  static String get DISTRICTS_V1 => '/api/v1/datas/thailand/district';
  static String get SUB_DISTRICTS_V1 => '/api/v1/datas/thailand/sub-district';
  static String get ZIPCODE_V1 => '/api/v1/datas/thailand/zipcode';

  static String get UPDATE_ADDRESS => '/api/app/user/address';
  //-> Lives
  static String get GET_LIVES => '/api/lives/get';

  //-> Video feeds
  static String get videos => '/api/videos';
  static String get GET_VIDEO => '$videos/get?index={index}&length={length}';
  static String get LIKE_VIDEO => '$videos/like?video_id={video_id}';
  static String get LIKE_COMMENT_VIDEO => '$videos/comments/like?video_id={video_id}&comment_id={commentId}';
  static String get REMOVE_COMMENT_VIDEO => '$videos/comments/remove';
  static String get UPDATE_COMMENT_VIDEO => '$videos/comments/update';
  static String get GET_COMMENTS_VIDEO => '$videos/comments/get?video_id={video_id}&index={index}&length={length}';
  static String get GET_VIDEO_BY_ID => '/api/videos/get/{video_id}';
  static String get GET_COUNT_ORDER => '/api/v1/orders/count';

  //-> Affiliate
  static String get affiliate => '/api/affiliate';
  static String get GET_PRODUCTS => '/api/v1/products';
  static String get REGISTER_AFFILIATE => '$affiliate/register';
  static String get SEND_EMAIL_AFFILIATE => '$affiliate/email/send';
  static String get VERIFY_EMAIL_AFFILIATE => '$affiliate/email/verify';
  static String get AFFILIATE_OFFERS_PRODUCTS => '$affiliate/offers-products';

  static String get AFFILIATE_PROFILE => '$affiliate/profile/get';
  static String get UPDATE_AFFILIATE_PROFILE => '$affiliate/profile/update';
  static String get DATAS_BANKS => '$affiliate/datas/banks';
  static String get VERIFY_UPDATE_REGISTER_BANK => '$affiliate/verify/update';
  static String get GET_VERIFY_REGISTER_BANK => '$affiliate/verify/get';
  static String get GET_ORDERS => '$affiliate/reports/orders';
  static String get GENERATE_AFFILIATE_LINK => '$affiliate/custom-link';
  static String get AFFILIATE_CATEGORIES => '/api/categories';
  static String get AFFILIATE_CATEGORIES_TAB => '$affiliate/profile/tab-categories';
  static String get AFFILIATE_DASHBOARD_SUMMARY => '$affiliate/reports/dashboard/summary';
  static String get GET_BILLINGS => '$affiliate/reports/billings';
  static String get GET_BILLINGS_SUMMARY => '$affiliate/reports/billings/summary';
  static String get GET_BILLINGS_DETAIL => '$affiliate/reports/billings/get-dt';
  static String get GET_SOCIAL_PLATFORMS => '$affiliate/profile/social/get';
  static String get UPDATE_SOCIAL_PLATFORMS => '$affiliate/profile/social/update';
  static String get DELETE_SOCIAL_PLATFORMS => '$affiliate/profile/social/remove';

  static String get AFFILIATE_DASHBOARD_REPORT => '$affiliate/reports/dashboard';
  static String get AFFILIATE_TOP_SELLER => '$affiliate/reports/dashboard/top-seller';

  // -> Refund
  static String get REFUND => '/api/refund';
  static String get UPDATE_PAYMENT => '/api/refund/update-payment';
  static String get REFUND_CF_RETURN => '/api/refund/confirm-return';

  static String get GET_BANNERS => '/api/v1/datas/banners?checked_case={checkedCase}';
  static String get CHECKOUT => '/api/checkout';
  static String get GET_ACTIVE_PAYMENT_GATEWAYS => '/api/v1/payments/get?master_order_id={master_order_id}&checked=1';
  static String get UPDATE_PAYMENT_METHOD => '/api/v1/master-orders/update-payment?master_order_id={master_order_id}&gateway_id={gateway_id}';
}

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/login/cubit/timer_cubit.dart';
import 'package:shop_chill_app/screens/login/cubit/timer_state.dart';
import 'package:shop_chill_app/screens/login/otp_confirmation.dart';
import 'package:shop_chill_app/screens/login/widgets/phone_text_form_field.dart';
import 'package:shop_chill_app/screens/login/widgets/reusable_social_login_buttons.dart';
import 'package:shop_chill_app/screens/login/widgets/submit_button.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_logo.dart';

import '../../backend/api_requests/api_calls.dart';
import '../../config/shopchill_loading/shopchill_loading.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  _RegisterPageState createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  TextEditingController phoneController = TextEditingController();
  final ValueNotifier<bool> isEnabledValue = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    phoneController.addListener(_validateInput);
  }

  void _validateInput() {
    final isCountdown = context.read<WorkoutCubit>().state is WorkoutInProgress;

    final isValid = phoneController.text.isNotEmpty && phoneController.text.length == 10 && !isCountdown;
    isEnabledValue.value = isValid;
  }

  @override
  void dispose() {
    phoneController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }
        _onPushToLoginButtonPressed();
      },

      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
          appBar: _buildAppBar(context),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const ShopchillLogo(),
                  20.gap,
                  PhoneTextFormField(phoneController: phoneController),
                  10.gap,
                  _buildSendOTPButtonSection(),
                  10.gap,
                  _buildSocialAuthSection(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSendOTPButtonSection() {
    return BlocConsumer<WorkoutCubit, WorkoutState>(
      listener: (context, state) {
        _validateInput();
      },
      builder: (context, state) {
        return SubmitButton(
          isEnabled: isEnabledValue,
          onTap: () async => _onRequestOTPProcess(),
          label: state is WorkoutInProgress ? 'ส่งได้อีกครั้งในอีก ${state.elapsed}' : 'ขอรหัส OTP',
        );
      },
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => _onPushToLoginButtonPressed(),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('สมัครสมาชิกใหม่', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),
      centerTitle: true,
      elevation: 0,
    );
  }

  void _onRequestOTPProcess() async {
    if (phoneController.text.isEmpty) {
      ShopChillLoading.showError("กรุณาระบุหมายเลขโทรศัพท์");

      return;
    }

    showLoading();
    try {
      await RegisterCall().registerOtp(phone: phoneController.text).then((value) {
        hideLoading();
        if (value['status'] == true) {
          BlocProvider.of<WorkoutCubit>(context).startWorkout(0);

          Navigator.pushReplacement(
            context,
            CupertinoPageRoute(
              builder: (context) => OtpConfirmation(mobile: phoneController.text, refCode: value['ref_code'], type: ConfirmationOtpType.register),
            ),
          );
        } else {
          ShopChillLoading.showError(value['message'] ?? value['msg']);
        }
      });
    } catch (e, stackTrace) {
      debugPrintStack(label: 'Error: $e', stackTrace: stackTrace);
    } finally {
      hideLoading();
    }
  }

  Widget _buildSocialAuthSection(BuildContext context) {
    return Expanded(
      child: Container(
        alignment: Alignment.bottomCenter,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  const Divider(),
                  Container(
                    margin: const EdgeInsets.only(bottom: 5),
                    color: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: AutoSizeText(
                      'หรือสมัครสมาชิกด้วย',
                      textAlign: TextAlign.center,
                      style: FlutterFlowTheme.of(context).subtitle1.copyWith(color: ColorThemeConfig.unfocusedTextColor),
                      maxFontSize: 14,
                    ),
                  ),
                ],
              ),
              20.gap,
              const ReusableSocialLoginButtons(),
              60.gap,
              _buildCanLoginSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCanLoginSection(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text('หากคุณมีบัญชีผู้ใช้แล้วคุณสามารถ', style: Theme.of(context).textTheme.labelMedium),
        5.gap,
        GestureDetector(
          onTap: () async => _onPushToLoginButtonPressed(),
          child: Text(
            'เข้าสู่ระบบ',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(color: ColorThemeConfig.primaryColor, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  void _onPushToLoginButtonPressed() {
    Navigator.pushNamedAndRemoveUntil(
      context,
      AppRoutes.login,
      (route) => route.settings.name == AppRoutes.home || route.settings.name == AppRoutes.navBar,
    );
  }
}

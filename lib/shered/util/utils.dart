import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:mime/mime.dart';
import 'package:shop_chill_app/screens/affiliate/models/address_model.dart';

import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/shered/widgets/dialog/custom_alert_dialog.dart';

import '../../config/shopchill_loading/shopchill_loading.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:flutter_line_sdk/flutter_line_sdk.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/screens/affiliate/bloc/affiliate_custom_links_bloc/affiliate_custom_links_bloc.dart';
import 'package:shop_chill_app/screens/chat/blocs/allchat_cubit/all_chat_room_cubit.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';

import '../../auth/auth_util.dart';

class Utils {
  static String convertToShortForm(double number) {
    const units = [
      {'value': 1e12, 'suffix': 'T'},
      {'value': 1e9, 'suffix': 'B'},
      {'value': 1e6, 'suffix': 'M'},
      {'value': 1e3, 'suffix': 'K'},
    ];

    for (final unit in units) {
      final double unitValue = unit['value'] as double;
      final String suffix = unit['suffix'] as String;

      if (number >= unitValue) {
        final shortValue = number / unitValue;
        final formatted = shortValue % 1 == 0 ? shortValue.toStringAsFixed(0) : shortValue.toStringAsFixed(1);
        return '$formatted$suffix';
      }
    }

    // Return without formatting if number is less than 1,000
    return number % 1 == 0 ? number.toStringAsFixed(0) : number.toStringAsFixed(2);
  }

  static String toThaiShortForm(double number) {
    final sign = number < 0 ? '-' : '';
    final absNumber = number.abs();

    if (absNumber >= 1e12) {
      return '$sign${(absNumber / 1e12).toStringAsFixed(1)} ล้านล้าน';
    } else if (absNumber >= 1e9) {
      return '$sign${(absNumber / 1e9).toStringAsFixed(1)} พันล้าน';
    } else if (absNumber >= 1e6) {
      return '$sign${(absNumber / 1e6).toStringAsFixed(1)} ล้าน';
    } else if (absNumber >= 1e3) {
      return '$sign${(absNumber / 1e3).toStringAsFixed(1)} พัน';
    } else {
      return '$sign${absNumber.toStringAsFixed(0)}';
    }
  }

  // ฟังก์ชันสำหรับลบช่องว่าง
  static String removeSpaces(String text) {
    return text.replaceAll(RegExp(r"\s+"), "");
  }

  //แปลงไฟล์ภาพเป็น Base64
  Future<String> convertImageToBase64(File image) async {
    final bytes = await image.readAsBytes();
    final mimeType = lookupMimeType(image.path) ?? "image/png"; // ตรวจสอบ MIME type
    final base64Image = "data:$mimeType;base64,${base64Encode(bytes)}";
    return base64Image;
  }

  static String formatAddressDetail(AddressModel? address) {
    if (address == null) return '-';
    final parts = [
      address.addressAddress,
      if (address.addressStreet?.isNotEmpty ?? false) address.addressStreet,
      if (address.addressAlley?.isNotEmpty ?? false) address.addressAlley,
      if (address.addressGroupArea?.isNotEmpty ?? false) address.addressGroupArea,
      if (address.addressBuilding?.isNotEmpty ?? false) address.addressBuilding,
      if (address.addressVillage?.isNotEmpty ?? false) address.addressVillage,
      address.district,
      address.subDistrict,
      "จังหวัด${address.province}",
      address.zipCode,
    ];
    return parts.where((part) => part != null && part.isNotEmpty).join(', ');
  }

  Future<void> saveImageToGallery(String imageUrl) async {
    try {
      // ขอ permission
      final status = await Permission.storage.request();
      final statusPhotos = await Permission.photos.request();

      if (status.isGranted || statusPhotos.isGranted) {
        final response = await http.get(Uri.parse(imageUrl));

        if (response.statusCode == 200) {
          final result = await ImageGallerySaverPlus.saveImage(
            Uint8List.fromList(response.bodyBytes),
            quality: 80,
            name: "shopchill_${DateTime.now().millisecondsSinceEpoch}",
          );

          if ((result['isSuccess'] ?? false) == true) {
            ShopChillLoading.showSuccess("บันทึกรูปภาพเรียบร้อยแล้ว");
          } else {
            ShopChillLoading.showError("บันทึกรูปภาพไม่สำเร็จ");
          }
        } else {
          ShopChillLoading.showError("โหลดรูปภาพไม่สำเร็จ (${response.statusCode})");
        }
      } else {
        ShopChillLoading.showError("ไม่ได้รับสิทธิ์เข้าถึงคลังรูปภาพ");
      }
    } catch (e) {
      ShopChillLoading.showError("เกิดข้อผิดพลาด: $e");
    }
  }

  String maskPhone(String raw) {
    // ตัวอย่าง: 08X-XXX-12XX
    if (raw.length < 6) return raw;
    final start = raw.substring(0, 2);
    final end = raw.substring(raw.length - 2);
    return '$start*${"*" * (raw.length - 4)}$end';
  }

  String maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2) return email;
    final name = parts[0];
    final domain = parts[1];
    final visible = name.length <= 2 ? name[0] : name.substring(0, 2);
    return '$visible***@$domain';
  }

  Future<void> onLogoutProcess(BuildContext context, {required bool routeIsFirst}) async {
    try {
      showLoading();
      await signOut();
      FFAppState().token = "";
      FFAppState().uid = '';
      await FacebookAuth.instance.logOut();
      await LineSDK.instance.logout();
      await FirebaseAuth.instance.signOut();

      context.read<UserBloc>().add(const RefreshUserEvent());
      context.read<CartBloc>().add(GetCartEvent());
      context.read<AllChatRoomCubit>().oninitial();
      // await context.read<UserBloc>().stream.first;
      //clear affiliate info
      context.read<AffiliateCustomLinksBloc>().add(ClearAffiliateInfo());
      // Navigator.of(context, rootNavigator: true).pop();
      if (routeIsFirst) {
        Navigator.popUntil(context, (route) => route.isFirst);
      }
    } catch (e, t) {
      hideLoading();
      debugPrintStack(label: 'Logout Error: $e', stackTrace: t);
      showMessageDialog(context, message: e.toString());
    } finally {
      hideLoading();
    }
  }

  void showMessageDialog(BuildContext context, {required String? message, VoidCallback? onComfirm}) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dContext) {
        return CustomAlertDialog(
          title: 'แจ้งเตือนจากระบบ',
          description: message ?? 'เกิดข้อผิดพลาดกรุณาลองใหม่',
          onCancel: () => Navigator.of(context, rootNavigator: true).pop(),
          btnConfirmText: 'ตกลง',
          onConfirm: onComfirm,
        );
      },
    );
  }
}

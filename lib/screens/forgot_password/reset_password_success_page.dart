import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';

import '../flutter_flow/flutter_flow_theme.dart';
import 'package:flutter/material.dart';

class ResetPasswordSuccessPage extends StatefulWidget {
  const ResetPasswordSuccessPage({super.key});

  @override
  _ResetPasswordSuccessPageState createState() => _ResetPasswordSuccessPageState();
}

class _ResetPasswordSuccessPageState extends State<ResetPasswordSuccessPage> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }
        _onPushToLoginButtonPressed();
      },
      child: Scaffold(
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        body: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Safe<PERSON>rea(
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(UImageAssets.GROUP_80, fit: BoxFit.cover),
                        20.gap,
                        Text(
                          'ตั้งรหัสผ่านสำเร็จ',
                          style: FlutterFlowTheme.of(
                            context,
                          ).subtitle2.override(fontFamily: 'Sarabun', color: FlutterFlowTheme.of(context).black600, useGoogleFonts: false),
                        ),
                        Text('กรุณาเข้าสู่ระบบด้วยรหัสผ่านใหม่', style: FlutterFlowTheme.of(context).bodyText1),
                      ],
                    ),
                  ),
                  _buildLoginButton(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 54,

      padding: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(color: ColorThemeConfig.newPrimaryColor, borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: _onPushToLoginButtonPressed,
        child: Center(
          child: Text(
            'เข้าสู่ระบบ',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
          ),
        ),
      ),
    );
  }

  void _onPushToLoginButtonPressed() {
    Navigator.pushNamedAndRemoveUntil(
      context,
      AppRoutes.login,
      (route) => route.settings.name == AppRoutes.home || route.settings.name == AppRoutes.navBar,
    );
  }
}

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/backend/schema/local_database/local_database.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/register/models/provider_auth_response.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';
import 'package:shop_chill_app/shered/util/dialog.dart';

class SocialAuthUtils {
  static final SocialAuthUtils _instance = SocialAuthUtils._internal();
  factory SocialAuthUtils() => _instance;
  SocialAuthUtils._internal();

  Future<ProviderAuthResponse?> checkOrReActivateAndLogin<T>({
    required BuildContext context,
    required String? uid,
    required Future<List<T>> Function() getLocalProfiles,
    required Future<ProviderAuthResponse?> Function() callLogin,
    required Future<void> Function(String? uid) deleteProfileByUid,
  }) async {
    final isExist = (await getLocalProfiles()).any((e) {
      if (e is FacebookProfile) return e.uid == uid;
      if (e is GoogleProfile) return e.uid == uid;
      if (e is AppleProfile) return e.uid == uid;
      if (e is LineProfile) return e.uid == uid;
      return false;
    });

    if (isExist) {
      final completer = Completer<ProviderAuthResponse>();
      ShopChillDialogs().showReactivationAccountDialog(
        context,
        callback: () async {
          await deleteProfileByUid(uid);
          final response = await callLogin();
          completer.complete(response);
        },
      );
      return completer.future;
    } else {
      return callLogin();
    }
  }

  bool getStatusFromResponse(LoginFromWhere provider, ApiCallResponse response) {
    switch (provider) {
      case LoginFromWhere.facebook:
        return LoginWithFacebookCall.status(response.jsonBody ?? '');
      case LoginFromWhere.google:
        return LoginWithGoogleCall.status(response.jsonBody ?? '');
      case LoginFromWhere.apple:
        return LoginWithAppleCall.status(response.jsonBody ?? '');
      case LoginFromWhere.line:
        return LoginWithLineCall.status(response.jsonBody ?? '');
      case LoginFromWhere.others:
        return false;
    }
  }

  String? getTokenFromResponse(LoginFromWhere provider, ApiCallResponse response) {
    switch (provider) {
      case LoginFromWhere.facebook:
        return LoginWithFacebookCall.token(response.jsonBody ?? '').toString();
      case LoginFromWhere.google:
        return LoginWithGoogleCall.token(response.jsonBody ?? '').toString();
      case LoginFromWhere.apple:
        return LoginWithAppleCall.token(response.jsonBody ?? '')?.toString();
      case LoginFromWhere.line:
        return LoginWithLineCall.token(response.jsonBody ?? '')?.toString();
      case LoginFromWhere.others:
        return null;
    }
  }

  void handleError(BuildContext context, LoginFromWhere provider, ApiCallResponse response) {
    switch (provider) {
      case LoginFromWhere.facebook:
        showError(LoginWithFacebookCall.message(response.jsonBody ?? ''));
        break;
      case LoginFromWhere.google:
        showError(LoginWithGoogleCall.message(response.jsonBody ?? ''));
        break;
      case LoginFromWhere.apple:
        showError(LoginWithAppleCall.message(response.jsonBody ?? ''));
        break;
      case LoginFromWhere.line:
        showError(LoginWithLineCall.message(response.jsonBody ?? ''));
        break;
      case LoginFromWhere.others:
        break;
    }
  }
}

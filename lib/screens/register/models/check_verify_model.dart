class CheckVerifyResponseModel {
  CheckVerifyResponseModel({
    required this.status,
    required this.code,
    required this.message,
    required this.data,
    required this.onetimeToken,
    this.resetPasswordToken,
    required this.user,
  });

  final bool? status;
  final String? code;
  final String? message;
  final Data? data;
  final String? onetimeToken;
  final String? resetPasswordToken;
  final UserData? user;

  CheckVerifyResponseModel copyWith({
    bool? status,
    String? code,
    String? message,
    Data? data,
    String? onetimeToken,
    String? resetPasswordToken,
    UserData? user,
  }) {
    return CheckVerifyResponseModel(
      status: status ?? this.status,
      code: code ?? this.code,
      message: message ?? this.message,
      data: data ?? this.data,
      onetimeToken: onetimeToken ?? this.onetimeToken,
      resetPasswordToken: resetPasswordToken ?? this.resetPasswordToken,
      user: user ?? this.user,
    );
  }

  factory CheckVerifyResponseModel.fromJson(Map<String, dynamic> json) {
    return CheckVerifyResponseModel(
      status: json["status"],
      code: json["code"],
      message: json["message"] ?? json["msg"],
      data: json["data"] != null && json["data"] is Map<String, dynamic> ? Data.fromJson(json["data"]) : null,
      onetimeToken: json["onetime_token"],
      resetPasswordToken: json["reset_password_token"],
      user: json["user"] != null && json["user"] is Map<String, dynamic> ? UserData.fromJson(json["user"]) : null,
    );
  }

  Map<String, dynamic> toJson() => {
    "status": status,
    "code": code,
    "message": message,
    "data": data?.toJson(),
    "onetime_token": onetimeToken,
    "reset_password_token": resetPasswordToken,
    "user": user?.toJson(),
  };
}

class Data {
  Data({required this.otpId, required this.result, required this.isErrorCount, required this.isExprCode});

  final String? otpId;
  final bool? result;
  final bool? isErrorCount;
  final bool? isExprCode;

  Data copyWith({String? otpId, bool? result, bool? isErrorCount, bool? isExprCode}) {
    return Data(
      otpId: otpId ?? this.otpId,
      result: result ?? this.result,
      isErrorCount: isErrorCount ?? this.isErrorCount,
      isExprCode: isExprCode ?? this.isExprCode,
    );
  }

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(otpId: json["otpId"], result: json["result"], isErrorCount: json["isErrorCount"], isExprCode: json["isExprCode"]);
  }

  Map<String, dynamic> toJson() => {"otpId": otpId, "result": result, "isErrorCount": isErrorCount, "isExprCode": isExprCode};
}

class UserData {
  UserData({
    required this.id,
    required this.name,
    required this.email,
    required this.emailVerifiedAt,
    required this.username,
    required this.avatar,
    required this.phone,
    required this.phoneVerifiedAt,
    required this.apiToken,
    required this.onetimeLoginToken,
    required this.resetPasswordToken,
    required this.ishipId,
    required this.ishipToken,
    required this.facebookId,
    required this.facebookToken,
    required this.facebookStatus,
    required this.lineId,
    required this.lineToken,
    required this.lineStatus,
    required this.googleId,
    required this.googleToken,
    required this.googleStatus,
    required this.appleId,
    required this.appleToken,
    required this.appleStatus,
    required this.fcmTokens,
    required this.status,
    required this.userStatus,
    required this.deletedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final String? name;
  final String? email;
  final dynamic emailVerifiedAt;
  final String? username;
  final String? avatar;
  final String? phone;
  final dynamic phoneVerifiedAt;
  final String? apiToken;
  final String? onetimeLoginToken;
  final dynamic resetPasswordToken;
  final dynamic ishipId;
  final dynamic ishipToken;
  final String? facebookId;
  final String? facebookToken;
  final int? facebookStatus;
  final dynamic lineId;
  final dynamic lineToken;
  final dynamic lineStatus;
  final String? googleId;
  final String? googleToken;
  final int? googleStatus;
  final String? appleId;
  final String? appleToken;
  final int? appleStatus;
  final dynamic fcmTokens;
  final int? status;
  final dynamic userStatus;
  final dynamic deletedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  UserData copyWith({
    int? id,
    String? name,
    String? email,
    dynamic emailVerifiedAt,
    String? username,
    String? avatar,
    String? phone,
    dynamic phoneVerifiedAt,
    String? apiToken,
    String? onetimeLoginToken,
    dynamic resetPasswordToken,
    dynamic ishipId,
    dynamic ishipToken,
    String? facebookId,
    String? facebookToken,
    int? facebookStatus,
    dynamic lineId,
    dynamic lineToken,
    dynamic lineStatus,
    String? googleId,
    String? googleToken,
    int? googleStatus,
    String? appleId,
    String? appleToken,
    int? appleStatus,
    dynamic fcmTokens,
    int? status,
    dynamic userStatus,
    dynamic deletedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserData(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      username: username ?? this.username,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
      apiToken: apiToken ?? this.apiToken,
      onetimeLoginToken: onetimeLoginToken ?? this.onetimeLoginToken,
      resetPasswordToken: resetPasswordToken ?? this.resetPasswordToken,
      ishipId: ishipId ?? this.ishipId,
      ishipToken: ishipToken ?? this.ishipToken,
      facebookId: facebookId ?? this.facebookId,
      facebookToken: facebookToken ?? this.facebookToken,
      facebookStatus: facebookStatus ?? this.facebookStatus,
      lineId: lineId ?? this.lineId,
      lineToken: lineToken ?? this.lineToken,
      lineStatus: lineStatus ?? this.lineStatus,
      googleId: googleId ?? this.googleId,
      googleToken: googleToken ?? this.googleToken,
      googleStatus: googleStatus ?? this.googleStatus,
      appleId: appleId ?? this.appleId,
      appleToken: appleToken ?? this.appleToken,
      appleStatus: appleStatus ?? this.appleStatus,
      fcmTokens: fcmTokens ?? this.fcmTokens,
      status: status ?? this.status,
      userStatus: userStatus ?? this.userStatus,
      deletedAt: deletedAt ?? this.deletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json["id"],
      name: json["name"],
      email: json["email"],
      emailVerifiedAt: json["email_verified_at"],
      username: json["username"],
      avatar: json["avatar"],
      phone: json["phone"],
      phoneVerifiedAt: json["phone_verified_at"],
      apiToken: json["api_token"],
      onetimeLoginToken: json["onetime_login_token"],
      resetPasswordToken: json["reset_password_token"],
      ishipId: json["iship_id"],
      ishipToken: json["iship_token"],
      facebookId: json["facebook_id"],
      facebookToken: json["facebook_token"],
      facebookStatus: json["facebook_status"],
      lineId: json["line_id"],
      lineToken: json["line_token"],
      lineStatus: json["line_status"],
      googleId: json["google_id"],
      googleToken: json["google_token"],
      googleStatus: json["google_status"],
      appleId: json["apple_id"],
      appleToken: json["apple_token"],
      appleStatus: json["apple_status"],
      fcmTokens: json["fcm_tokens"],
      status: json["status"],
      userStatus: json["user_status"],
      deletedAt: json["deleted_at"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "email": email,
    "email_verified_at": emailVerifiedAt,
    "username": username,
    "avatar": avatar,
    "phone": phone,
    "phone_verified_at": phoneVerifiedAt,
    "api_token": apiToken,
    "onetime_login_token": onetimeLoginToken,
    "reset_password_token": resetPasswordToken,
    "iship_id": ishipId,
    "iship_token": ishipToken,
    "facebook_id": facebookId,
    "facebook_token": facebookToken,
    "facebook_status": facebookStatus,
    "line_id": lineId,
    "line_token": lineToken,
    "line_status": lineStatus,
    "google_id": googleId,
    "google_token": googleToken,
    "google_status": googleStatus,
    "apple_id": appleId,
    "apple_token": appleToken,
    "apple_status": appleStatus,
    "fcm_tokens": fcmTokens,
    "status": status,
    "user_status": userStatus,
    "deleted_at": deletedAt,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };
}

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:shop_chill_app/screens/login/widgets/social_login_buttons.dart';
import 'package:shop_chill_app/screens/register/services/social_auth_service.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';

class ReusableSocialLoginButtons extends StatefulWidget {
  const ReusableSocialLoginButtons({super.key});

  @override
  State<ReusableSocialLoginButtons> createState() => _ReusableSocialLoginButtonsState();
}

final _authService = SocialAuthService();

class _ReusableSocialLoginButtonsState extends State<ReusableSocialLoginButtons> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        SocialLoginButtons(
          svgIconPath: UImageAssets.FACEBOOK_SVG,
          onTap: () async => _authService.authenticate(context, provider: ProviderType.facebook),
        ),
        SocialLoginButtons(
          svgIconPath: UImageAssets.LINE_SVG,
          onTap: () async => _authService.authenticate(context, provider: ProviderType.line),
        ),
        SocialLoginButtons(
          svgIconPath: UImageAssets.GOOGLE_SVG,
          onTap: () async => _authService.authenticate(context, provider: ProviderType.google),
        ),

        if (Platform.isIOS)
          SocialLoginButtons(
            svgIconPath: UImageAssets.APPLE_SVG,
            onTap: () async => _authService.authenticate(context, provider: ProviderType.apple),
          ),
      ],
    );
  }
}

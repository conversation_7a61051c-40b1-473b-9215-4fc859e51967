import 'dart:io';

import 'package:badges/badges.dart' as badges;
import 'package:badges/badges.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:local_auth/local_auth.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/address/address_widget.dart';
import 'package:shop_chill_app/screens/affiliate/screens/profile_affiliate/widgets/setting_option_card.dart';
import 'package:shop_chill_app/screens/affiliate/screens/profile_affiliate/widgets/setting_option_trailing.dart';
import 'package:shop_chill_app/screens/affiliate/widgets/x_text_form_field.dart';
import 'package:shop_chill_app/screens/my_account/repository/user_repository.dart';
import 'package:shop_chill_app/screens/my_account/widgets/setting_section_card.dart';
import 'package:shop_chill_app/screens/notification/bloc/notification_bloc.dart';
import 'package:shop_chill_app/screens/setting/bloc/setting_bloc.dart';
import 'package:shop_chill_app/screens/setting/bloc/setting_event.dart';
import 'package:shop_chill_app/screens/setting/bloc/setting_state.dart';
import 'package:shop_chill_app/screens/setting/models/profile_setting_response.dart';
import 'package:shop_chill_app/screens/setting/pages/about_shopchill_page.dart';
import 'package:shop_chill_app/screens/setting/pages/delete_account_page.dart';
import 'package:shop_chill_app/screens/setting/pages/privacy_page.dart';
import 'package:shop_chill_app/screens/setting/pages/term_of_service_page.dart';
import 'package:shop_chill_app/shered/device/app_info_service.dart';
import 'package:shop_chill_app/shered/util/dialog.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/util/utils.dart';
import 'package:shop_chill_app/shered/widgets/back_button.dart';

import '../../config/shopchill_loading/shopchill_loading.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

import '../forgot_password/forgot_password_request_paget.dart';
import '../my_account/bloc/user/user_bloc.dart';
import '../notification/pages/notification_setting/notification_setting.dart';

class SettingWidget extends StatefulWidget {
  const SettingWidget({super.key});

  @override
  _SettingWidgetState createState() => _SettingWidgetState();
}

class _SettingWidgetState extends State<SettingWidget> {
  final scaffoldKey = GlobalKey<ScaffoldState>();
  TextEditingController? nameController;
  TextEditingController? userNameController;
  TextEditingController? oldPassController;
  TextEditingController? newPassController;
  TextEditingController? cfPassController;

  final formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();

  final _storage = const FlutterSecureStorage();
  final String KEY_USER_PROFILE = "KEY_USER_PROFILE";
  final LocalAuthentication localAuth = LocalAuthentication();

  @override
  void initState() {
    super.initState();
    context.read<UserBloc>().add(GetUserProfile());
    context.read<SettingBloc>().add(FetchUserProfile());
    nameController = TextEditingController();
    userNameController = TextEditingController();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(key: scaffoldKey, appBar: _buildAppBar(context), backgroundColor: FlutterFlowTheme.of(context).primaryBackground, body: _buildBody());
  }

  Widget _buildBody() {
    return BlocBuilder<SettingBloc, SettingState>(
      builder: (context, state) {
        if (state is SettingLoading) {
          return _buildLoading(context);
        } else if (state is SettingLoaded) {
          final profileSetting = state.profileSettingResponse.data?.profile ?? Profile();

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPersonalInformationSection(context, profileSetting),
                  10.gap,
                  _helpCenterSection(context),
                  10.gap,
                  _buildDeleteAccountAndLogoutSection(context),
                  20.gap,
                ],
              ),
            ),
          );
        } else if (state is SettingError) {
          return Center(child: Text(state.message));
        }
        return const Center(child: Text("No Data"));
      },
    );
  }

  Padding _buildCustomDivider() {
    return const Padding(
      padding: EdgeInsets.only(left: 20),
      child: Divider(height: 1.5, color: Colors.black26),
    );
  }

  Widget _buildPersonalInformationSection(BuildContext context, Profile profileSetting) {
    userNameController?.text = profileSetting.username ?? "";
    final emptyUserName = (userNameController?.text ?? '').isEmpty;
    nameController?.text = profileSetting.name ?? "";
    final email = profileSetting.email ?? "";
    return Column(
      children: [
        _buildProfilePicture(context),
        BlocBuilder<UserBloc, UserState>(
          builder: (context, state) {
            return SettingSectionCard(
              header: "ข้อมูลส่วนตัว",
              children: [
                SettingOptionCard(
                  title: "ชื่อผู้ใช้",
                  trailing: SettingOptionTrailing(
                    text: emptyUserName ? 'N/A' : userNameController?.text,
                    maxLines: 2,
                    width: MediaQuery.of(context).size.width / 1.5,
                    isActive: emptyUserName,
                    withArrow: emptyUserName,
                  ),
                  onTap: emptyUserName ? () => _showModalEditUsername(profileSetting) : null,
                ),
                _buildCustomDivider(),
                SettingOptionCard(
                  title: "Email",
                  trailing: SettingOptionTrailing(text: email.isEmpty ? null : email.toString(), maxLines: 2, width: MediaQuery.of(context).size.width / 1.5),
                  onTap: null,
                ),
                _buildCustomDivider(),
                SettingOptionCard(
                  title: "ชื่อ - นามสกุล",
                  trailing: SettingOptionTrailing(text: nameController?.text, maxLines: 2, isActive: true, withArrow: true),
                  onTap: () => state is UserLoaded ? _showModalEditPersonalInformation(profileSetting) : null,
                ),
                _buildCustomDivider(),
                SettingOptionCard(
                  title: "เปลี่ยนรหัสผ่าน",
                  trailing: const SettingOptionTrailing(text: '', isActive: true, withArrow: true),
                  onTap: () => state is UserLoaded ? _showModalChangePassword(profileSetting) : null,
                ),
                _buildCustomDivider(),
                SettingOptionCard(
                  title: "ที่อยู่ในการจัดส่ง",
                  trailing: const SettingOptionTrailing(text: '', isActive: true, withArrow: true),
                  onTap: state is UserLoaded
                      ? () async {
                          context.read<UserBloc>().add(const GetMyAddress());

                          await Navigator.push(context, CupertinoPageRoute(builder: (context) => const AddressWidget(addressId: null, noAddress: true)));
                        }
                      : null,
                ),
              ],
            );
          },
        ),
      ],
    );
  }
  /*[old]
    Widget _buildPersonalInformationSection(BuildContext context, Profile profileSetting) {
    final username = profileSetting.username ?? "";
    final name = profileSetting.name ?? "";
    final email = profileSetting.email ?? "";
    return Column(
      children: [
        _buildProfilePicture(context),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildTitle('ข้อมูลส่วนตัว'),
            InkWell(
              onTap: () => _showModalEditPersonalInformation(profileSetting),
              child: const CircleAvatar(
                backgroundColor: Colors.transparent,
                child: Icon(
                  FontAwesomeIcons.penToSquare,
                  size: 20,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ),
        _buildCustomField('ชื่อผู้ใช้', username.isEmpty ? 'ยังไม่ระบุ' : username.toString()),
        8.gap,
        _buildCustomField('Email', email),
        8.gap,
        _buildCustomField('ชื่อ - นามสกุล', name, canEdit: true),
      ],
    );
  } */

  Widget _buildCustomField(String label, String text, {bool canEdit = false}) {
    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
      width: double.infinity,
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontWeight: FontWeight.w400)),
          5.gap,
          Text(text, style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 16, color: canEdit ? null : Colors.grey.shade400)),
        ],
      ),
    );
  }

  Widget _buildProfilePicture(BuildContext context) {
    return BlocBuilder<SettingBloc, SettingState>(
      builder: (context, state) {
        return state is SettingLoading
            ? const CircleAvatar(radius: 70, backgroundColor: Colors.white)
            : state is SettingLoaded
            ? GestureDetector(
                onTap: () => _showImagePicker(context, state.profileSettingResponse.data?.profile),
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
                  child: ClipOval(
                    child: CircleAvatar(
                      radius: 50,
                      backgroundColor: Colors.white,
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: Image.network(
                              state.profileSettingResponse.data?.profile?.avatar ?? "",
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(Icons.person, size: MediaQuery.of(context).size.width / 5, color: Colors.grey.shade300);
                              },
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              color: Colors.black54,
                              padding: const EdgeInsets.all(8),
                              alignment: Alignment.bottomCenter,
                              child: const Text(
                                'แก้ไข',
                                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            : const SizedBox.shrink();
      },
    );
  }

  Widget _buildPasswordSettingSection(BuildContext context, Profile profileSetting) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildTitle('ตั้งค่ารหัสผ่าน'),
            InkWell(
              onTap: () => _showModalChangePassword(profileSetting),
              child: const CircleAvatar(
                backgroundColor: Colors.transparent,
                child: Icon(FontAwesomeIcons.penToSquare, size: 20, color: Colors.black),
              ),
            ),
          ],
        ),
        Container(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 14, 8, 10),
          width: double.infinity,
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
          child: Text("**************", style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 16)),
        ),
      ],
    );
  }

  Future<void> _showModalEditPersonalInformation(Profile profileSetting) async {
    nameController?.text = profileSetting.name ?? "";
    await showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        WidgetsBinding.instance.addPostFrameCallback((_) => ShopChillLoading.dismiss());

        return SingleChildScrollView(
          child: Container(
            width: double.infinity,
            padding: MediaQuery.of(context).viewInsets,
            color: const Color(0xFF737373),
            child: Container(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 20),
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Color(0xffF9F9F9),
                borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  _buildHeaderModalBottomSheet("แก้ไขข้อมูลส่วนตัว"),
                  Container(
                    padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                    width: double.infinity,
                    decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(16)),
                    child: _buildFullNameField(),
                  ),
                  20.gap,
                  _buildSubmitButton(btnName: 'บันทึก', onTap: () => updateProfile(profileSetting)),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _showModalEditUsername(Profile profileSetting) async {
    userNameController?.text = profileSetting.username ?? "";
    await showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        WidgetsBinding.instance.addPostFrameCallback((_) => ShopChillLoading.dismiss());

        return SingleChildScrollView(
          child: Container(
            width: double.infinity,
            padding: MediaQuery.of(context).viewInsets,
            color: const Color(0xFF737373),
            child: Container(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 20),
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Color(0xffF9F9F9),
                borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  _buildHeaderModalBottomSheet("แก้ไขข้อมูลส่วนตัว"),
                  Container(
                    padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                    width: double.infinity,
                    decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(16)),
                    child: _buildUserNameField(),
                  ),
                  20.gap,
                  _buildSubmitButton(btnName: 'บันทึก', onTap: () => updateProfile(profileSetting)),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFullNameField() {
    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 8, 8),
      width: double.infinity,
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ชื่อ - นามสกุล',
            style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: const Color(0xff808080), fontWeight: FontWeight.w400),
          ),
          5.gap,
          XTextFormField(
            controller: nameController,
            keyboardType: TextInputType.name,
            hintText: 'ระบุชื่อ - นามสกุล',
            validator: (val) {
              if (val == null || val.trim().isEmpty) {
                return 'กรุณากรอก ชื่อ - นามสกุล';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUserNameField() {
    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 8, 8),
      width: double.infinity,
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ชื่อผู้ใช้',
            style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: const Color(0xff808080), fontWeight: FontWeight.w400),
          ),
          5.gap,
          XTextFormField(
            controller: userNameController,
            keyboardType: TextInputType.name,
            hintText: 'ชื่อผู้ใช้',
            validator: (val) {
              if (val == null || val.trim().isEmpty) {
                return 'กรุณากรอก ชื่อผู้ใช้';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderModalBottomSheet(String title) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 4,
            width: 50,
            margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
            decoration: BoxDecoration(color: const Color(0xff9B9B9B), borderRadius: BorderRadius.circular(16)),
          ),
          5.gap,
          Text(title, style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: const Color(0xff18191A))),
          10.gap,
        ],
      ),
    );
  }

  void _showModalChangePassword(Profile profileSetting) async {
    oldPassController = TextEditingController();
    newPassController = TextEditingController();
    cfPassController = TextEditingController();
    await showModalBottomSheet(
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(25.0))),
      context: context,
      builder: (context) {
        return SingleChildScrollView(
          child: Container(
            width: double.infinity,
            padding: MediaQuery.of(context).viewInsets,
            color: const Color(0xFF737373),
            child: Container(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 20),
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Color(0xffF9F9F9),
                borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeaderModalBottomSheet("เปลี่ยนรหัสผ่าน"),
                  _buildPasswordField(profileSetting, context),
                  20.gap,
                  _buildSubmitButton(btnName: 'บันทึกรหัสผ่าน', onTap: () => _onSubmitResetPassword()),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPasswordField(Profile profileSetting, BuildContext context) {
    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.disabled,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          _buildOldPasswordField(context),
          _buildForgotPasswordButton(context),
          Container(
            padding: const EdgeInsetsDirectional.all(16),
            width: double.infinity,
            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
            child: Column(children: [_buildNewPasswordField(context), 10.gap, _buildConfirmPasswordField(context)]),
          ),
        ],
      ),
    );
  }

  Widget _buildForgotPasswordButton(BuildContext context) {
    return Container(
      alignment: const AlignmentDirectional(1, 0),
      padding: const EdgeInsetsDirectional.fromSTEB(0, 10, 0, 10),
      child: InkWell(
        onTap: () async {
          await Navigator.push(
            context,
            PageTransition(
              type: PageTransitionType.rightToLeft,
              duration: const Duration(milliseconds: 0),
              reverseDuration: const Duration(milliseconds: 0),
              child: const ForgotPasswordRequestPage(),
            ),
          ).then((v) {
            Navigator.pop(context);
            context.read<SettingBloc>().add(FetchUserProfile());
          });
        },
        child: Text(
          'ลืมรหัสผ่าน?',
          style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: FlutterFlowTheme.of(context).primaryColor, fontWeight: FontWeight.w400),
        ),
      ),
    );
  }

  Widget _buildOldPasswordField(BuildContext context) {
    return Container(
      padding: const EdgeInsetsDirectional.all(16),
      width: double.infinity,
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'รหัสผ่านเดิม',
            style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: const Color(0xff808080), fontWeight: FontWeight.w400),
          ),
          5.gap,
          XTextFormField(
            obscureText: true,
            controller: oldPassController,
            keyboardType: TextInputType.name,
            hintText: 'ระบุรหัสผ่านเดิม',
            validator: (val) {
              if (val == null || val.isEmpty) {
                return 'กรุณากรอก รหัสผ่านเดิม';
              }
              return null;
            },
          ),
          /*  TextFormField(
            obscureText: true,
            controller: oldPassController,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            decoration: InputDecoration(
              labelStyle: FlutterFlowTheme.of(context).bodyText2,
              hintText: '',
              hintStyle: FlutterFlowTheme.of(context).bodyText2,
              filled: true,
              fillColor: FlutterFlowTheme.of(context).secondaryBackground,
              contentPadding: EdgeInsets.zero,
              errorStyle: FlutterFlowTheme.of(context).subtitle2.copyWith(color: FlutterFlowTheme.of(context).alternate),
            ),
            style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 14),
            validator: (val) {
              if (val == null || val.isEmpty) {
                return 'กรุณากรอก รหัสผ่านเดิม';
              }
              return null;
            },
          ), */
        ],
      ),
    );
  }

  Widget _buildNewPasswordField(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'รหัสผ่านใหม่',
          style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: const Color(0xff808080), fontWeight: FontWeight.w400),
        ),
        5.gap,
        XTextFormField(
          obscureText: true,
          controller: newPassController,
          keyboardType: TextInputType.name,
          hintText: 'ระบุรหัสผ่านใหม่',
          validator: (val) {
            if (val == null || val.isEmpty) {
              return 'กรุณากรอก รหัสผ่านใหม่';
            }
            return null;
          },
        ),
        /*  TextFormField(
          obscureText: true,
          controller: newPassController,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            labelStyle: FlutterFlowTheme.of(context).bodyText2,
            hintText: '',
            hintStyle: FlutterFlowTheme.of(context).bodyText2,
            filled: true,
            fillColor: FlutterFlowTheme.of(context).secondaryBackground,
            contentPadding: EdgeInsets.zero,
            errorStyle: FlutterFlowTheme.of(context).subtitle2.copyWith(color: FlutterFlowTheme.of(context).alternate),
          ),
          style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 14),
          validator: (val) {
            if (val == null || val.isEmpty) {
              return 'กรุณากรอก รหัสผ่านใหม่';
            }
            return null;
          },
        ), */
      ],
    );
  }

  Widget _buildConfirmPasswordField(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'กรอกรหัสผ่านใหม่อีกครั้ง',
          style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: const Color(0xff808080), fontWeight: FontWeight.w400),
        ),
        5.gap,
        XTextFormField(
          obscureText: true,
          controller: cfPassController,
          keyboardType: TextInputType.name,
          hintText: 'ระบุรหัสผ่านใหม่อีกครั้ง',
          validator: (val) {
            if (val == null || val.isEmpty) {
              return 'กรุณากรอก รหัสผ่านใหม่อีกครั้ง';
            }
            return null;
          },
        ),
        /* TextFormField(
          obscureText: true,
          controller: cfPassController,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            labelStyle: FlutterFlowTheme.of(context).bodyText2,
            hintText: '',
            hintStyle: FlutterFlowTheme.of(context).bodyText2,
            filled: true,
            fillColor: FlutterFlowTheme.of(context).secondaryBackground,
            contentPadding: EdgeInsets.zero,
            errorStyle: FlutterFlowTheme.of(context).subtitle2.copyWith(color: FlutterFlowTheme.of(context).alternate),
          ),
          style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 14),
          validator: (val) {
            if (val == null || val.isEmpty) {
              return 'กรุณากรอก รหัสผ่านใหม่อีกครั้ง';
            }
            return null;
          },
        ), */
      ],
    );
  }

  Widget _buildSubmitButton({required String btnName, void Function()? onTap}) {
    return Center(
      child: CupertinoButton(
        padding: const EdgeInsets.symmetric(horizontal: 80),
        color: Theme.of(context).primaryColor,
        onPressed: onTap,
        child: Text(
          btnName,
          style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16),
        ),
      ),
    );
    // return InkWell(
    //   onTap: onTap,
    //   child: Container(
    //     width: double.infinity,
    //     alignment: Alignment.center,
    //     height: 50,
    //     decoration: BoxDecoration(
    //       borderRadius: BorderRadius.circular(10),
    //       color: FlutterFlowTheme.of(context).primaryColor,

    //     ),
    //     child: Text(
    //       btnName,
    //       textAlign: TextAlign.center,
    //       style:FlutterFlowTheme.of(context).bodyText3.copyWith(fontSize: 16, color: Colors.white),
    //     ),
    //   ),
    // );
  }

  void _onSubmitResetPassword() {
    if (formKey.currentState == null || !formKey.currentState!.validate()) {
      showError('กรุณากรอกข้อมูลให้ครบถ้วน');
      return;
    }
    if (newPassController!.text.length < 8) {
      showError('รหัสผ่านต้องยาวอย่างน้อย 8 ตัวอักษร');
      return;
    } else if (newPassController!.text != cfPassController!.text) {
      showError('รหัสผ่านไม่ตรงกัน');
      return;
    }
    _resetPasswordProcess();
  }

  Widget _buildSocialConnectButtonSection() {
    return Column(children: [_buildTitle('การเชื่อมต่อ'), _buildConnectToGoogleBtn(), _buildConnectToFacebook(), _buildConnectToApple()]);
  }

  Widget _helpCenterSection(BuildContext context) {
    return SettingSectionCard(
      header: "ศูนย์ช่วยเหลือ",
      children: [
        BlocBuilder<UserBloc, UserState>(
          builder: (context, state) {
            return state is UserLoaded
                ? Column(
                    children: [
                      SettingOptionCard(
                        title: "การแจ้งเตือน",
                        trailing: const SettingOptionTrailing(text: '', isActive: true, withArrow: true),
                        onTap: () {
                          context.read<NotificationBloc>().add(NotificationInitialSettingEvent(context));
                          Navigator.push(context, CupertinoPageRoute(builder: (context) => const NotificationSetting()));
                        },
                      ),
                      _buildCustomDivider(),
                    ],
                  )
                : const SizedBox();
          },
        ),

        SettingOptionCard(
          title: 'ข้อกำหนดและเงื่อนไขการใช้งาน',
          trailing: const SettingOptionTrailing(text: '', isActive: true, withArrow: true),
          onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const TermOfServicePage())),
        ),
        _buildCustomDivider(),
        SettingOptionCard(
          title: "Privacy Policy",
          trailing: const SettingOptionTrailing(text: '', isActive: true, withArrow: true),
          onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const PrivacyPage())),
        ),
        _buildCustomDivider(),
        _buildAboutShopChillSection(context),
      ],
    );
  }

  Widget _buildAboutShopChillSection(BuildContext context) {
    return SettingOptionCard(
      title: "เกี่ยวกับ Shop Chill",
      trailing: SizedBox(
        width: MediaQuery.of(context).size.width / 2.5,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Text(
                  AppInfoService.isNewVersion ? 'new version: ${AppInfoService.status?.storeVersion ?? ''}' : AppInfoService.appVersion,
                  style: FlutterFlowTheme.of(context).bodyText3.copyWith(fontSize: 14, color: ColorThemeConfig.unfocusedTextColor),
                  textAlign: TextAlign.right,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            if (AppInfoService.isNewVersion)
              badges.Badge(
                position: badges.BadgePosition.topStart(),
                badgeStyle: badges.BadgeStyle(
                  shape: BadgeShape.circle,
                  badgeColor: Colors.red,
                  borderRadius: BorderRadius.circular(6),
                  // padding: const EdgeInsets.symmetric(horizontal: 6),
                  padding: const EdgeInsets.only(bottom: 3, top: 1, left: 10, right: 10),
                ),
                badgeContent: const Text(
                  '1',
                  style: TextStyle(fontSize: 10, color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
          ],
        ),
      ),
      onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const AboutShopChillPage())),
    );
  }

  /* [old]
  Widget _helpCenterSection(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildTitle('ศูนย์ช่วยเหลือ'),
        BlocBuilder<UserBloc, UserState>(
          builder: (context, state) {
            return state is UserLoaded
                ? _buildSettingButton(
                    label: 'การแจ้งเตือน',
                    onTap: () {
                      context.read<NotificationBloc>().add(NotificationInitialSettingEvent(context));
                      Navigator.push(context, CupertinoPageRoute(builder: (context) => const NotificationSetting()));
                    },
                  )
                : const SizedBox.shrink();
          },
        ),
        5.gap,
        /*  _buildSettingButton(
          label: 'Languages',
          trailingText: 'TH',
          onTap: () {},
        ),
        5.gap,
        _buildSettingButton(
          label: 'ติดต่อเจ้าหน้าที่',
          onTap: () {},
        ), */
        5.gap,
        _buildSettingButton(
          label: 'Privacy Policy ff',
          onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const PrivacyPage())),
        ),
        5.gap,
      ],
    );
  } */

  Widget _buildTitle(String title) {
    return Container(
      alignment: Alignment.centerLeft,
      margin: const EdgeInsets.only(top: 20, bottom: 10),
      child: Text(title, style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: const Color(0xff18191A))),
    );
  }

  Widget _buildSettingButton({required String label, String? trailingText, required Function() onTap}) {
    return ListTile(
      tileColor: Colors.white,
      dense: false,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      minVerticalPadding: 18.0,
      contentPadding: const EdgeInsets.only(right: 16, left: 16),
      onTap: onTap,
      leading: Text(label, style: FlutterFlowTheme.of(context).subtitle2.copyWith(fontSize: 14, color: const Color(0xff18191A))),
      trailing: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (trailingText != null)
            Container(
              margin: const EdgeInsets.only(right: 10),
              child: Text(trailingText, style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: const Color(0xff18191A))),
            ),
          const Icon(Icons.arrow_forward_ios, size: 20, color: Colors.black),
        ],
      ),
    );
  }

  Widget _buildLoading(BuildContext context) {
    return Center(
      child: SizedBox(width: 50, height: 50, child: CircularProgressIndicator(color: FlutterFlowTheme.of(context).primaryColor)),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
      automaticallyImplyLeading: false,
      leading: ShopChillBackButton(onBack: () => Navigator.pop(context)),
      title: Text('ตั้งค่าโปรไฟล์', style: Theme.of(context).textTheme.titleLarge),
      centerTitle: true,
      elevation: 0,
    );
  }

  Widget _buildConnectSwitchTile({required String title, required bool value, required Function(bool) onChanged, required bool isConnected}) {
    return SwitchListTile.adaptive(
      value: value,
      onChanged: onChanged,
      title: Row(
        children: [
          Text(title, style: FlutterFlowTheme.of(context).subtitle2),
          const SizedBox(width: 16),
          Visibility(
            visible: isConnected,
            child: Text('[เชื่อมต่อแล้ว]', style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: const Color(0xFF4B39EF))),
          ),
        ],
      ),
      tileColor: Colors.white,
      activeColor: const Color(0xFF4B39EF),
      activeTrackColor: const Color(0x8D4B39EF),
      dense: false,
      controlAffinity: ListTileControlAffinity.trailing,
    );
  }

  Widget _buildConnectToApple() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return _buildConnectSwitchTile(
            title: 'เชื่อมต่อ Apple',
            value: state.isConnectToApple,
            onChanged: (newValue) {
              context.read<UserBloc>().add(ConnectToApple(isConnectToApple: !state.isConnectToApple));
            },
            isConnected: state.isConnectToApple,
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget _buildConnectToFacebook() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return _buildConnectSwitchTile(
            title: 'เชื่อมต่อ Facebook',
            value: state.isConnectToFacebook,
            onChanged: (newValue) {
              context.read<UserBloc>().add(ConnectToFacebook(isConnectToFacebook: !state.isConnectToFacebook));
            },
            isConnected: state.isConnectToFacebook,
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget _buildConnectToGoogleBtn() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return _buildConnectSwitchTile(
            title: 'เชื่อมต่อ Google',
            value: state.isConnectToGoogle,
            onChanged: (newValue) {
              context.read<UserBloc>().add(ConnectToGoogle(isConnectToGoogle: !state.isConnectToGoogle));
            },
            isConnected: state.isConnectToGoogle,
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget _buildDeleteAccountAndLogoutSection(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        if (userState is UserLoaded) {
          return BlocBuilder<SettingBloc, SettingState>(
            builder: (context, state) {
              return state is SettingLoaded
                  ? Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => DeleteAccountPage(profile: state.profileSettingResponse.data?.profile?.avatar ?? '', name: nameController?.text ?? ''),
                                ),
                              );
                            },
                            child: Container(
                              padding: const EdgeInsetsDirectional.all(12),
                              margin: const EdgeInsetsDirectional.fromSTEB(0, 10, 0, 0),
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: const BorderRadius.all(Radius.circular(10)),
                                border: Border.fromBorderSide(BorderSide(color: Colors.grey.shade300, width: .8)),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.delete, color: Colors.black),
                                  5.gap,
                                  Text('ลบบัญชีผู้ใช้', style: FlutterFlowTheme.of(context).bodyText3.copyWith(fontSize: 14, color: Colors.black)),
                                ],
                              ),
                            ),
                          ),
                        ),
                        10.gap,
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              ShopChillDialogs().showDialogSureToLogOut(context, userState.userModel!);
                            },
                            child: Container(
                              padding: const EdgeInsetsDirectional.all(12),
                              margin: const EdgeInsetsDirectional.fromSTEB(0, 10, 0, 0),
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.black,
                                borderRadius: const BorderRadius.all(Radius.circular(10)),
                                border: Border.fromBorderSide(BorderSide(color: Colors.grey.shade300, width: .8)),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.logout, color: Colors.white),
                                  5.gap,
                                  Text('ออกจากระบบ', style: FlutterFlowTheme.of(context).bodyText3.copyWith(fontSize: 14, color: Colors.white)),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                  : const SizedBox();
            },
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }

  void _showImagePicker(BuildContext context, Profile? profile) {
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: const Text('เลือกรูปภาพ'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              _handleImageSelection(ImageSource.gallery, profile);
            },
            child: const Text('แกลเลอรี่'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              _handleImageSelection(ImageSource.camera, profile);
            },
            child: const Text('ถ่ายรูป'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('ยกเลิก'),
        ),
      ),
    );
  }

  Future<void> _handleImageSelection(ImageSource source, Profile? profile) async {
    Navigator.pop(context);

    try {
      final XFile? image = await _picker.pickImage(source: source);
      if (image == null) return;

      ShopChillLoading.show(status: 'กำลังอัปโหลด');

      final base64Image = await Utils().convertImageToBase64(File(image.path));
      final path = await UserRepository().getPathImage(image: base64Image);

      context.read<SettingBloc>().add(UpdateUserProfile(avatar: path, name: profile?.name, username: profile?.username, email: profile?.email.toString().replaceAll('@', '1@')));

      await _storage.write(key: KEY_USER_PROFILE, value: path);
    } catch (e, t) {
      debugPrint("เกิดข้อผิดพลาดในการเลือกรูปภาพ: $e \nt: $t");

      final PermissionStatus permissionStatus = (source == ImageSource.camera) ? await Permission.camera.request() : await Permission.photos.request();

      // Handle denied permissions
      if (permissionStatus.isPermanentlyDenied) {
        _showPermissionDialog();

        return;
      }
    } finally {
      ShopChillLoading.dismiss();
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          title: const Center(child: Text('โปรดอนุญาตการเข้าถึง')),
          content: const Text('กรุณาเปิดการเข้าถึงกล้องหรือแกลเลอรี่ในการตั้งค่าแอปพลิเคชัน', textAlign: TextAlign.center),
          actions: [
            TextButton(
              child: Text('ยกเลิก', style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: Colors.black)),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text('ไปที่การตั้งค่า', style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: ColorThemeConfig.primaryColor)),
              onPressed: () async {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  void updateProfile(Profile profile) async {
    if (userNameController!.text.isEmpty) {
      showError('กรุณาระบุชื่อผู้ใช้!');

      return;
    }
    if (nameController!.text.isEmpty) {
      showError('กรุณากรอกชื่อ');
      return;
    }

    context.read<SettingBloc>().add(
      UpdateUserProfile(avatar: profile.avatar, name: nameController?.text, username: userNameController?.text, email: profile.email.toString().replaceAll('@', '1@')),
    );

    setState(() => Navigator.pop(context));
  }

  Future<void> _resetPasswordProcess() async {
    context.read<SettingBloc>().add(
      ResetPasswordEvent(context: context, oldPassword: oldPassController!.text, newPassword: newPassController!.text, confirmPassword: cfPassController!.text),
    );
  }
}

/* [old code]

import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_ShopChillLoading/flutter_ShopChillLoading.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/screens/notification/bloc/notification_bloc.dart';
import 'package:shop_chill_app/screens/setting/pages/delete_account_page.dart';
import 'package:shop_chill_app/screens/setting/pages/privacy_page.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';

import '../../backend/api_requests/api_calls.dart';
import '../../backend/api_requests/api_manager.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

import '../forgotpassword_one/forgotpassword_one_widget.dart';
import '../myaccount/bloc/user/user_bloc.dart';
import '../myaccount/repository/user_repository.dart';
import '../notification/pages/notification_setting/notification_setting.dart';
import '../../shered/util/dialog.dart';

class SettingWidget extends StatefulWidget {
  const SettingWidget({Key? key}) : super(key: key);

  @override
  _SettingWidgetState createState() => _SettingWidgetState();
}

class _SettingWidgetState extends State<SettingWidget> {
  final ShopChillDialogs _modalBottomSheetRepository = ShopChillDialogs();
  final UserRepository _userRepository = UserRepository();
  TextEditingController? textController1;
  TextEditingController? textController2;
  bool isFaceID = false;

  bool? switchListTileValue;
  bool? switchListTileValue2;
  bool? switchListTileValue3;
  String? profile;
  String? email;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  TextEditingController? nameController;
  TextEditingController? userNameController;
  TextEditingController? oldPassController;
  TextEditingController? newPassController;
  TextEditingController? cfPassController;

  final formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();

  final _storage = const FlutterSecureStorage();
  final String KEY_LOCAL_AUTH_ENABLED = "KEY_LOCAL_AUTH_ENABLED";
  final String KEY_USER_PROFILE = "KEY_USER_PROFILE";
  final LocalAuthentication localAuth = LocalAuthentication();

  late List<BiometricType> biometricType;

  @override
  void initState() {
    super.initState();
    textController1 = TextEditingController();
    textController2 = TextEditingController();
    nameController = TextEditingController();
    userNameController = TextEditingController();
    _getAuthEnable();
  }

  _getAuthEnable() async {
    String isLocalAuthEnabled = await _storage.read(key: KEY_LOCAL_AUTH_ENABLED) ?? "false";

    biometricType = await localAuth.getAvailableBiometrics();

    if ('true' == isLocalAuthEnabled) {
      setState(() {
        isFaceID = true;
      });
    } else {
      setState(() {
        isFaceID = false;
      });
    }
  }

  _faceVerify() async {
    bool canCheckBiometrics = await localAuth.canCheckBiometrics;

    if (canCheckBiometrics) {
      List<BiometricType> availableBiometrics = await localAuth.getAvailableBiometrics();

      if (availableBiometrics.contains(BiometricType.face)) {
        try {
          bool authenticated = await localAuth.authenticate(
            localizedReason: 'Please authenticate using Face ID to proceed.',
            options: const AuthenticationOptions(
              stickyAuth: true,
              biometricOnly: true,
            ),
          );

          if (authenticated) {
            final userState = BlocProvider.of<UserBloc>(context).state;

            if (userState is UserLoaded) {
              print('Face ID verification successful.');
              // Proceed with your desired functionality

              await _storage.write(key: KEY_LOCAL_AUTH_ENABLED, value: "true");
              setState(() {
                isFaceID = true;
              });
            }
          } else {
            print('Face ID verification failed.');
            await _storage.write(key: KEY_LOCAL_AUTH_ENABLED, value: "false");
            setState(() {
              isFaceID = false;
            });
            // Handle the failure scenario
          }
        } catch (e) {
          await _storage.write(key: KEY_LOCAL_AUTH_ENABLED, value: "false");
          setState(() {
            isFaceID = false;
          });
          print('Error: $e');
          // Handle the error scenario
        }
      } else {
        print('Face ID is not available on this device.');
      }
    } else {
      print('Biometric authentication is not available on this device.');
    }
  }

  Future chooseImage(int choose, BuildContext context) async {
    if (choose == 0) {
      XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      final byte = File(image!.path).readAsBytesSync();
      String base64Image = "data:image/png;base64,${base64Encode(byte)}";
      final path = await UserRepository().getPathImage(image: base64Image);

      ShopChillLoading.show(status: 'กำลังอัปโหลด');
      await UserRepository().updateProfile(
        avatar: path,
        name: nameController!.text,
        email: email.toString().replaceAll('@', '1@'),
        username: userNameController!.text,
      );
      await _storage.write(key: KEY_USER_PROFILE, value: path);
      // context.read<UserBloc>().add(GetUserProfile());
      setState(() {});
    } else {
      XFile? image = await _picker.pickImage(source: ImageSource.camera);
      final byte = File(image!.path).readAsBytesSync();
      String base64Image = "data:image/png;base64,${base64Encode(byte)}";
      final path = await UserRepository().getPathImage(image: base64Image);

      ShopChillLoading.show(status: 'กำลังอัปโหลด');
      await UserRepository().updateProfile(
        avatar: path,
        name: nameController!.text,
        email: email.toString().replaceAll('@', '1@'),
        username: userNameController!.text,
      );
      await _storage.write(key: KEY_USER_PROFILE, value: path);
      setState(() {});
      // context.read<UserBloc>().add(GetUserProfile());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      appBar: responsiveVisibility(
        context: context,
        tablet: false,
        tabletLandscape: false,
        desktop: false,
      )
          ? AppBar(
              backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
              automaticallyImplyLeading: false,
              leading: FlutterFlowIconButton(
                borderColor: Colors.transparent,
                borderRadius: 30,
                borderWidth: 1,
                buttonSize: 60,
                icon: FaIcon(
                  FontAwesomeIcons.angleLeft,
                  color: Colors.black,
                  size: 30,
                ),
                onPressed: () async {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                'ตั้งค่า',
                style: FlutterFlowTheme.of(context).title2.override(
                      fontFamily: 'Sarabun',
                      color: Colors.black,
                      fontSize: 20,
                      useGoogleFonts: false,
                    ),
              ),
              actions: [],
              centerTitle: true,
              elevation: 0,
            )
          : null,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      body: FutureBuilder<ApiCallResponse>(
        future: GetProfileCall.call(
          token: FFAppState().token,
        ),
        builder: (context, snapshot) {
          // Customize what your widget looks like when it's loading.
          if (!snapshot.hasData) {
            return Center(
              child: SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  color: FlutterFlowTheme.of(context).primaryColor,
                ),
              ),
            );
          }
          final columnGetProfileResponse = snapshot.data!;
          nameController!.text = GetProfileCall.name(
            columnGetProfileResponse.jsonBody,
          ).toString();
          userNameController!.text = GetProfileCall.username(
            columnGetProfileResponse.jsonBody,
          ).toString();
          profile = GetProfileCall.avatar(
            columnGetProfileResponse.jsonBody,
          ).toString();
          email = GetProfileCall.email(
            columnGetProfileResponse.jsonBody,
          ).toString();
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 50),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  BlocBuilder<UserBloc, UserState>(builder: (context, state) {
                    if (state is UserLoaded) {
                      return Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              _showImagePicker(context);
                            },
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
                              child: ClipOval(
                                child: CircleAvatar(
                                  radius: 70,
                                  backgroundColor: Colors.transparent,
                                  backgroundImage: NetworkImage(profile!),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        bottom: 0,
                                        left: 0,
                                        right: 0,
                                        child: Container(
                                          color: Colors.black54,
                                          padding: EdgeInsets.all(8),
                                          child: Align(
                                            alignment: Alignment.bottomCenter,
                                            child: Text(
                                              'แก้ไข',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'ข้อมูลส่วนตัว',
                                  style: FlutterFlowTheme.of(context).subtitle2.override(
                                        fontFamily: 'Sarabun',
                                        color: const Color(0xff18191A),
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                                InkWell(
                                  onTap: () async {
                                    nameController!.text = GetProfileCall.name(
                                      columnGetProfileResponse.jsonBody,
                                    ).toString();
                                    await showModalBottomSheet(
                                      isScrollControlled: true,
                                      context: context,
                                      builder: (context) {
                                        return SingleChildScrollView(
                                          child: Padding(
                                            padding: MediaQuery.of(context).viewInsets,
                                            child: Container(
                                              height: 400,
                                              width: double.infinity,
                                              color: Color(0xFF737373),
                                              child: Container(
                                                padding: EdgeInsets.fromLTRB(24, 0, 24, 0),
                                                width: double.infinity,
                                                decoration: BoxDecoration(
                                                  color: Color(0xffF9F9F9),
                                                  borderRadius: BorderRadius.only(
                                                      topLeft: Radius.circular(16), topRight: Radius.circular(16), bottomRight: Radius.circular(0), bottomLeft: Radius.circular(0)),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisSize: MainAxisSize.max,
                                                  children: [
                                                    Align(
                                                      alignment: Alignment.center,
                                                      child: Container(
                                                        alignment: Alignment.center,
                                                        width: 60,
                                                        height: 6,
                                                        margin: EdgeInsets.all(16),
                                                        decoration: BoxDecoration(
                                                          color: Color(0xff9B9B9B),
                                                          borderRadius: BorderRadius.circular(16),
                                                        ),
                                                      ),
                                                    ),
                                                    Align(
                                                      alignment: Alignment.center,
                                                      child: Padding(
                                                        padding: EdgeInsets.fromLTRB(0, 8, 0, 16),
                                                        child: Text(
                                                          'แก้ไขข้อมูลส่วนตัว',
                                                          style: FlutterFlowTheme.of(context).subtitle1.override(
                                                                fontFamily: 'Sarabun',
                                                                color: Colors.black,
                                                                useGoogleFonts: false,
                                                              ),
                                                        ),
                                                      ),
                                                    ),
                                                    Container(
                                                      padding: EdgeInsets.fromLTRB(16, 0, 0, 0),
                                                      width: double.infinity,
                                                      decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius: BorderRadius.circular(8),
                                                      ),
                                                      child: Column(
                                                        mainAxisAlignment: MainAxisAlignment.center,
                                                        children: [
                                                          Container(
                                                            padding: EdgeInsetsDirectional.fromSTEB(0, 8, 8, 8),
                                                            width: double.infinity,
                                                            decoration: BoxDecoration(
                                                              color: Colors.white,
                                                              borderRadius: BorderRadius.circular(10.0),
                                                            ),
                                                            child: Column(
                                                              mainAxisSize: MainAxisSize.max,
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              children: [
                                                                if (userNameController != null && userNameController!.text.isEmpty) ...[
                                                                  Text(
                                                                    'Username',
                                                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                          fontFamily: 'Sarabun',
                                                                          color: Color(0xff808080),
                                                                          fontWeight: FontWeight.w400,
                                                                          useGoogleFonts: false,
                                                                        ),
                                                                  ),
                                                                  Padding(
                                                                    padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 8),
                                                                    child: Container(
                                                                      width: double.infinity,
                                                                      decoration: BoxDecoration(),
                                                                      child: TextFormField(
                                                                          controller: userNameController,
                                                                          autovalidateMode: AutovalidateMode.onUserInteraction,
                                                                          decoration: InputDecoration(
                                                                            labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                            hintText: '',
                                                                            hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                            filled: true,
                                                                            fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                                            contentPadding: EdgeInsetsDirectional.fromSTEB(8, 0, 0, 0),
                                                                            errorStyle: FlutterFlowTheme.of(context).subtitle2.override(
                                                                                  fontFamily: 'Sarabun',
                                                                                  color: FlutterFlowTheme.of(context).alternate,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                          style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                                fontFamily: 'Sarabun',
                                                                                fontSize: 16,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                          validator: (val) {
                                                                            if (val == null || val.isEmpty) {
                                                                              return 'กรุณากรอก Username';
                                                                            }
                                                                            return null;
                                                                          }),
                                                                    ),
                                                                  ),
                                                                ],
                                                                Text(
                                                                  'ชื่อ - นามสกุล',
                                                                  style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                        fontFamily: 'Sarabun',
                                                                        color: Color(0xff808080),
                                                                        fontWeight: FontWeight.w400,
                                                                        useGoogleFonts: false,
                                                                      ),
                                                                ),
                                                                Padding(
                                                                  padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                                                                  child: Container(
                                                                    width: double.infinity,
                                                                    decoration: BoxDecoration(),
                                                                    child: TextFormField(
                                                                        controller: nameController,
                                                                        autovalidateMode: AutovalidateMode.onUserInteraction,
                                                                        decoration: InputDecoration(
                                                                          labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                          hintText: '',
                                                                          hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                          filled: true,
                                                                          fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                                          contentPadding: EdgeInsetsDirectional.fromSTEB(8, 0, 0, 0),
                                                                          errorStyle: FlutterFlowTheme.of(context).subtitle2.override(
                                                                                fontFamily: 'Sarabun',
                                                                                color: FlutterFlowTheme.of(context).alternate,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                        style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                              fontFamily: 'Sarabun',
                                                                              fontSize: 16,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                        validator: (val) {
                                                                          if (val == null || val.isEmpty) {
                                                                            return 'กรุณากรอก ชื่อ - นามสกุล';
                                                                          }
                                                                          return null;
                                                                        }),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    InkWell(
                                                      onTap: () async {
                                                        updateProfile(columnGetProfileResponse);
                                                      },
                                                      child: Container(
                                                        width: double.infinity,
                                                        margin: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                                                        alignment: Alignment.center,
                                                        height: 60,
                                                        decoration: BoxDecoration(
                                                          image: DecorationImage(
                                                            colorFilter: new ColorFilter.mode(Colors.black.withOpacity(1), BlendMode.dstATop),
                                                            fit: BoxFit.cover,
                                                            image: Image.asset(
                                                              // 'assets/images/button_bg.png',
                                                              UImageAssets.BUTTON_BG,
                                                            ).image,
                                                          ),
                                                          borderRadius: BorderRadius.circular(30),
                                                        ),
                                                        child: Padding(
                                                          padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                                                          child: Text(
                                                            'บันทึก',
                                                            textAlign: TextAlign.center,
                                                            style: FlutterFlowTheme.of(context).title2.override(
                                                                  fontFamily: 'Sarabun',
                                                                  color: Colors.white,
                                                                  fontSize: 20,
                                                                  fontWeight: FontWeight.bold,
                                                                  useGoogleFonts: false,
                                                                ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  },
                                  child: Image.asset(
                                    // 'assets/images/ic_edit.png',
                                    UImageAssets.IC_EDIT,
                                    width: 20,
                                    height: 20,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Username',
                                  style: FlutterFlowTheme.of(context).bodyText1.override(
                                        fontFamily: 'Sarabun',
                                        fontWeight: FontWeight.w400,
                                        useGoogleFonts: false,
                                      ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(0, 5, 0, 0),
                                  child: Text(
                                    GetProfileCall.username(
                                      columnGetProfileResponse.jsonBody,
                                    ).toString().isEmpty
                                        ? 'ยังไม่ระบุ'
                                        : GetProfileCall.username(
                                            columnGetProfileResponse.jsonBody,
                                          ).toString(),
                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                          fontFamily: 'Sarabun',
                                          fontSize: 16,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
                            margin: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'ชื่อ - นามสกุล',
                                  style: FlutterFlowTheme.of(context).bodyText1.override(
                                        fontFamily: 'Sarabun',
                                        fontWeight: FontWeight.w400,
                                        useGoogleFonts: false,
                                      ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(0, 5, 0, 0),
                                  child: Text(
                                    GetProfileCall.name(
                                      columnGetProfileResponse.jsonBody,
                                    ).toString(),
                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                          fontFamily: 'Sarabun',
                                          fontSize: 16,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
                            margin: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Email',
                                  style: FlutterFlowTheme.of(context).bodyText1.override(
                                        fontFamily: 'Sarabun',
                                        fontWeight: FontWeight.w400,
                                        useGoogleFonts: false,
                                      ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(0, 5, 0, 0),
                                  child: Text(
                                    GetProfileCall.email(
                                      columnGetProfileResponse.jsonBody,
                                    ).toString(),
                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                          fontFamily: 'Sarabun',
                                          fontSize: 16,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'ตั้งค่ารหัสผ่าน',
                                  style: FlutterFlowTheme.of(context).subtitle2.override(
                                        fontFamily: 'Sarabun',
                                        color: const Color(0xff18191A),
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                                InkWell(
                                  onTap: () async {
                                    oldPassController = TextEditingController();
                                    newPassController = TextEditingController();
                                    cfPassController = TextEditingController();
                                    await showModalBottomSheet(
                                      isScrollControlled: true,
                                      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(25.0))),
                                      context: context,
                                      builder: (context) {
                                        return Padding(
                                          padding: EdgeInsets.only(top: 20, right: 20, left: 20, bottom: MediaQuery.of(context).viewInsets.bottom),
                                          child: SingleChildScrollView(
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Align(
                                                  alignment: Alignment.center,
                                                  child: Container(
                                                    alignment: Alignment.center,
                                                    width: 60,
                                                    height: 6,
                                                    margin: EdgeInsets.all(16),
                                                    decoration: BoxDecoration(
                                                      color: Color(0xff9B9B9B),
                                                      borderRadius: BorderRadius.circular(16),
                                                    ),
                                                  ),
                                                ),
                                                Align(
                                                  alignment: Alignment.center,
                                                  child: Padding(
                                                    padding: EdgeInsets.fromLTRB(0, 8, 0, 16),
                                                    child: Text(
                                                      'เปลี่ยนรหัสผ่าน',
                                                      style: FlutterFlowTheme.of(context).subtitle1.override(
                                                            fontFamily: 'Sarabun',
                                                            color: Colors.black,
                                                            useGoogleFonts: false,
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                                Form(
                                                  key: formKey,
                                                  autovalidateMode: AutovalidateMode.disabled,
                                                  child: Column(
                                                    mainAxisAlignment: MainAxisAlignment.start,
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    mainAxisSize: MainAxisSize.max,
                                                    children: [
                                                      if (GetProfileCall.hasPassword(
                                                        columnGetProfileResponse.jsonBody,
                                                      )) ...[
                                                        Container(
                                                          padding: EdgeInsets.fromLTRB(16, 0, 0, 0),
                                                          width: double.infinity,
                                                          decoration: BoxDecoration(
                                                            color: Colors.white,
                                                            borderRadius: BorderRadius.circular(8),
                                                          ),
                                                          child: Column(
                                                            mainAxisAlignment: MainAxisAlignment.center,
                                                            children: [
                                                              Container(
                                                                padding: EdgeInsetsDirectional.fromSTEB(0, 8, 8, 8),
                                                                width: double.infinity,
                                                                decoration: BoxDecoration(
                                                                  color: Colors.white,
                                                                  borderRadius: BorderRadius.circular(10.0),
                                                                ),
                                                                child: Column(
                                                                  mainAxisSize: MainAxisSize.max,
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  children: [
                                                                    Text(
                                                                      'รหัสผ่านเดิม',
                                                                      style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                            fontFamily: 'Sarabun',
                                                                            color: Color(0xff808080),
                                                                            fontWeight: FontWeight.w400,
                                                                            useGoogleFonts: false,
                                                                          ),
                                                                    ),
                                                                    Padding(
                                                                      padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                                                                      child: Container(
                                                                        width: double.infinity,
                                                                        decoration: BoxDecoration(),
                                                                        child: TextFormField(
                                                                            obscureText: true,
                                                                            controller: oldPassController,
                                                                            autovalidateMode: AutovalidateMode.onUserInteraction,
                                                                            decoration: InputDecoration(
                                                                              labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                              hintText: '',
                                                                              hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                              filled: true,
                                                                              fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                                              contentPadding: EdgeInsetsDirectional.fromSTEB(8, 0, 0, 0),
                                                                              errorStyle: FlutterFlowTheme.of(context).subtitle2.override(
                                                                                    fontFamily: 'Sarabun',
                                                                                    color: FlutterFlowTheme.of(context).alternate,
                                                                                    useGoogleFonts: false,
                                                                                  ),
                                                                            ),
                                                                            style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                                  fontFamily: 'Sarabun',
                                                                                  fontSize: 14,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                            validator: (val) {
                                                                              if (val == null || val.isEmpty) {
                                                                                return 'กรุณากรอก รหัสผ่านเดิม';
                                                                              }
                                                                              return null;
                                                                            }),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        Align(
                                                          alignment: AlignmentDirectional(1, 0),
                                                          child: Padding(
                                                            padding: EdgeInsetsDirectional.fromSTEB(0, 10, 0, 0),
                                                            child: InkWell(
                                                              onTap: () async {
                                                                await Navigator.push(
                                                                  context,
                                                                  PageTransition(
                                                                    type: PageTransitionType.rightToLeft,
                                                                    duration: Duration(milliseconds: 0),
                                                                    reverseDuration: Duration(milliseconds: 0),
                                                                    child: ForgotpasswordOneWidget(),
                                                                  ),
                                                                );
                                                              },
                                                              child: Text(
                                                                'ลืมรหัสผ่าน?',
                                                                style: FlutterFlowTheme.of(context).subtitle2.override(
                                                                      fontFamily: 'Sarabun',
                                                                      color: FlutterFlowTheme.of(context).primaryColor,
                                                                      fontWeight: FontWeight.w400,
                                                                      useGoogleFonts: false,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                      Container(
                                                        padding: EdgeInsets.fromLTRB(16, 0, 0, 0),
                                                        width: double.infinity,
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: BorderRadius.circular(8),
                                                        ),
                                                        child: Column(
                                                          mainAxisAlignment: MainAxisAlignment.center,
                                                          children: [
                                                            Container(
                                                              padding: EdgeInsetsDirectional.fromSTEB(0, 8, 8, 8),
                                                              width: double.infinity,
                                                              decoration: BoxDecoration(
                                                                color: Colors.white,
                                                                borderRadius: BorderRadius.circular(10.0),
                                                              ),
                                                              child: Column(
                                                                mainAxisSize: MainAxisSize.max,
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  Text(
                                                                    'รหัสผ่านใหม่',
                                                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                          fontFamily: 'Sarabun',
                                                                          color: Color(0xff808080),
                                                                          fontWeight: FontWeight.w400,
                                                                          useGoogleFonts: false,
                                                                        ),
                                                                  ),
                                                                  Padding(
                                                                    padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                                                                    child: Container(
                                                                      width: double.infinity,
                                                                      decoration: BoxDecoration(),
                                                                      child: TextFormField(
                                                                          obscureText: true,
                                                                          controller: newPassController,
                                                                          autovalidateMode: AutovalidateMode.onUserInteraction,
                                                                          decoration: InputDecoration(
                                                                            labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                            hintText: '',
                                                                            hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                            filled: true,
                                                                            fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                                            contentPadding: EdgeInsetsDirectional.fromSTEB(8, 0, 0, 0),
                                                                            errorStyle: FlutterFlowTheme.of(context).subtitle2.override(
                                                                                  fontFamily: 'Sarabun',
                                                                                  color: FlutterFlowTheme.of(context).alternate,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                          style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                                fontFamily: 'Sarabun',
                                                                                fontSize: 14,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                          validator: (val) {
                                                                            if (val == null || val.isEmpty) {
                                                                              return 'กรุณากรอก รหัสผ่านใหม่';
                                                                            }
                                                                            return null;
                                                                          }),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      Container(
                                                        padding: EdgeInsets.fromLTRB(16, 0, 0, 0),
                                                        margin: EdgeInsets.fromLTRB(0, 8, 0, 0),
                                                        width: double.infinity,
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: BorderRadius.circular(8),
                                                        ),
                                                        child: Column(
                                                          mainAxisAlignment: MainAxisAlignment.center,
                                                          children: [
                                                            Container(
                                                              padding: EdgeInsetsDirectional.fromSTEB(0, 8, 8, 8),
                                                              width: double.infinity,
                                                              decoration: BoxDecoration(
                                                                color: Colors.white,
                                                                borderRadius: BorderRadius.circular(10.0),
                                                              ),
                                                              child: Column(
                                                                mainAxisSize: MainAxisSize.max,
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  Text(
                                                                    'กรอกรหัสผ่านใหม่อีกครั้ง',
                                                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                          fontFamily: 'Sarabun',
                                                                          color: Color(0xff808080),
                                                                          fontWeight: FontWeight.w400,
                                                                          useGoogleFonts: false,
                                                                        ),
                                                                  ),
                                                                  Padding(
                                                                    padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                                                                    child: Container(
                                                                      width: double.infinity,
                                                                      decoration: BoxDecoration(),
                                                                      child: TextFormField(
                                                                          obscureText: true,
                                                                          controller: cfPassController,
                                                                          autovalidateMode: AutovalidateMode.onUserInteraction,
                                                                          decoration: InputDecoration(
                                                                            labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                            hintText: '',
                                                                            hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                                                            filled: true,
                                                                            fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                                            contentPadding: const EdgeInsetsDirectional.fromSTEB(8, 0, 0, 0),
                                                                            errorStyle: FlutterFlowTheme.of(context).subtitle2.override(
                                                                                  fontFamily: 'Sarabun',
                                                                                  color: FlutterFlowTheme.of(context).alternate,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                          style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                                fontFamily: 'Sarabun',
                                                                                fontSize: 14,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                          validator: (val) {
                                                                            if (val == null || val.isEmpty) {
                                                                              return 'กรุณากรอก รหัสผ่านใหม่อีกครั้ง';
                                                                            }
                                                                            return null;
                                                                          }),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      InkWell(
                                                        onTap: () async {
                                                          if (formKey.currentState == null || !formKey.currentState!.validate()) {
                                                            return;
                                                          } else if (newPassController!.text.length < 8) {
                                                            showError('รหัสผ่านต้องยาวอย่างน้อย 8 ตัวอักษร');
                                                            return;
                                                          } else if (newPassController!.text != cfPassController!.text) {
                                                            showError('รหัสผ่านไม่ตรงกัน');
                                                            return;
                                                          }
                                                          repass();
                                                        },
                                                        child: Container(
                                                          width: double.infinity,
                                                          margin: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 16),
                                                          alignment: Alignment.center,
                                                          height: 60,
                                                          decoration: BoxDecoration(
                                                            image: DecorationImage(
                                                              colorFilter: new ColorFilter.mode(Colors.black.withOpacity(1), BlendMode.dstATop),
                                                              fit: BoxFit.cover,
                                                              image: Image.asset(
                                                                // 'assets/images/button_bg.png',
                                                                UImageAssets.BUTTON_BG,
                                                              ).image,
                                                            ),
                                                            borderRadius: BorderRadius.circular(30),
                                                          ),
                                                          child: Padding(
                                                            padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                                                            child: Text(
                                                              'บันทึกรหัสผ่าน',
                                                              textAlign: TextAlign.center,
                                                              style: FlutterFlowTheme.of(context).title2.override(
                                                                    fontFamily: 'Sarabun',
                                                                    color: Colors.white,
                                                                    fontSize: 20,
                                                                    fontWeight: FontWeight.bold,
                                                                    useGoogleFonts: false,
                                                                  ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  },
                                  child: Image.asset(
                                    // 'assets/images/ic_edit.png',
                                    UImageAssets.IC_EDIT,
                                    width: 20,
                                    height: 20,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Text(
                                //   'Name',
                                //   style:
                                //       FlutterFlowTheme.of(context).bodyText1.override(
                                //             fontFamily: 'Sarabun',
                                //             fontWeight: FontWeight.w400,
                                //             useGoogleFonts: false,
                                //           ),
                                // ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(0, 5, 0, 0),
                                  child: Text(
                                    "**************",
                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                          fontFamily: 'Sarabun',
                                          fontSize: 16,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'การเชื่อมต่อ',
                                  style: FlutterFlowTheme.of(context).subtitle2.override(
                                        fontFamily: 'Sarabun',
                                        color: const Color(0xff18191A),
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          _buildConnectToGoogleBtn(),
                          _buildConnectToFacebook(),
                          _buildConnectToApple(),
                        ],
                      );
                    } else {
                      return SizedBox();
                    }
                  }),
                  /* Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'การแจ้งเตือน',
                          style: FlutterFlowTheme.of(context).subtitle2.override(
                                fontFamily: 'Sarabun',
                                color: const Color(0xff18191A),
                                fontWeight: FontWeight.w600,
                                useGoogleFonts: false,
                              ),
                        ),
                      ],
                    ),
                  ),
                  SwitchListTile.adaptive(
                    value: switchListTileValue ??= true,
                    onChanged: (newValue) => setState(() => switchListTileValue = newValue),
                    title: Text(
                      'สินค้าลดราคา',
                      style: FlutterFlowTheme.of(context).subtitle2,
                    ),
                    tileColor: Colors.white,
                    activeColor: Color(0xFF4B39EF),
                    activeTrackColor: Color(0x8D4B39EF),
                    dense: false,
                    controlAffinity: ListTileControlAffinity.trailing,
                  ),
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0, 10, 0, 10),
                    child: SwitchListTile.adaptive(
                      value: switchListTileValue2 ??= true,
                      onChanged: (newValue) => setState(() => switchListTileValue2 = newValue),
                      title: Text(
                        'สินค้ามาใหม่',
                        style: FlutterFlowTheme.of(context).subtitle2,
                      ),
                      tileColor: Colors.white,
                      activeColor: Color(0xFF4B39EF),
                      activeTrackColor: Color(0x8D4B39EF),
                      dense: false,
                      controlAffinity: ListTileControlAffinity.trailing,
                    ),
                  ),
                  SwitchListTile.adaptive(
                    value: switchListTileValue3 ??= true,
                    onChanged: (newValue) => setState(() => switchListTileValue3 = newValue),
                    title: Text(
                      'สถานะการจัดส่งสินค้า',
                      style: FlutterFlowTheme.of(context).subtitle2,
                    ),
                    tileColor: Colors.white,
                    activeColor: Color(0xFF4B39EF),
                    activeTrackColor: Color(0x8D4B39EF),
                    dense: false,
                    controlAffinity: ListTileControlAffinity.trailing,
                  ),*/
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'ศูนย์ช่วยเหลือ',
                          style: FlutterFlowTheme.of(context).subtitle2.override(
                                fontFamily: 'Sarabun',
                                color: const Color(0xff18191A),
                                fontWeight: FontWeight.w600,
                                useGoogleFonts: false,
                              ),
                        ),
                      ],
                    ),
                  ),

                  /* Visibility(
                    visible: biometricType.contains(BiometricType.face),
                    child: SwitchListTile.adaptive(
                      value: isFaceID,
                      onChanged: (newValue) async {
                        setState(() {
                        isFaceID = newValue;
                      });
                        if(isFaceID == true){
                          _faceVerify();

                        } else {
                          await _storage.write(key: KEY_LOCAL_AUTH_ENABLED, value: "false");
                        }


                      },
                      title: Text(
                         isFaceID ? 'ปิดใช้งาน Face ID' :'เปิดใช้งาน Face ID',
                        style: FlutterFlowTheme.of(context).subtitle2,
                      ),
                      tileColor: Colors.white,
                      activeColor: Color(0xFF4B39EF),
                      activeTrackColor: Color(0x8D4B39EF),
                      dense: false,
                      controlAffinity: ListTileControlAffinity.trailing,
                    ),
                  ),*/
                  _buildNotificationSettingButton(context),
                  Container(
                    padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                    margin: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Languages',
                              style: FlutterFlowTheme.of(context).subtitle2.override(
                                    fontFamily: 'Sarabun',
                                    color: const Color(0xff18191A),
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                            ),
                            Row(
                              children: [
                                Text(
                                  'TH  ',
                                  style: FlutterFlowTheme.of(context).subtitle2.override(
                                        fontFamily: 'Sarabun',
                                        color: const Color(0xff18191A),
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  size: 20,
                                )
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                    margin: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'ติดต่อเจ้าหน้าที่',
                              style: FlutterFlowTheme.of(context).subtitle2.override(
                                    fontFamily: 'Sarabun',
                                    color: const Color(0xff18191A),
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 20,
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PrivacyPage(),
                      ),
                    ),
                    child: Container(
                      padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                      margin: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white,
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Privacy Policy',
                                style: FlutterFlowTheme.of(context).subtitle2.override(
                                      fontFamily: 'Sarabun',
                                      color: const Color(0xff18191A),
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 20,
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  _buildDeleteAccount(context),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  _buildNotificationSettingButton(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return GestureDetector(
            onTap: () {
              context.read<NotificationBloc>().add(NotificationInitialSettingEvent(context));
              Navigator.push(context, CupertinoPageRoute(builder: (context) => NotificationSetting()));
            },
            child: Container(
              padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              margin: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'การแจ้งเตือน',
                        style: FlutterFlowTheme.of(context).subtitle2.override(
                              fontFamily: 'Sarabun',
                              color: const Color(0xff18191A),
                              fontWeight: FontWeight.w600,
                              useGoogleFonts: false,
                            ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 20,
                      )
                    ],
                  ),
                ],
              ),
            ),
          );
        } else {
          return SizedBox();
        }
      },
    );
  }

  _buildConnectToApple() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return SwitchListTile.adaptive(
            value: state.isConnectToApple,
            onChanged: (newValue) async {
              context.read<UserBloc>().add(ConnectToApple(isConnectToApple: !state.isConnectToApple));
            },
            title: Row(
              children: [
                Text(
                  'เชื่อมต่อ Apple',
                  style: FlutterFlowTheme.of(context).subtitle2,
                ),
                SizedBox(width: 16),
                Visibility(
                  visible: state.isConnectToApple,
                  child: Text('[เชื่อมต่อแล้ว]', style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: Color(0xFF4B39EF))),
                ),
              ],
            ),
            tileColor: Colors.white,
            activeColor: Color(0xFF4B39EF),
            activeTrackColor: Color(0x8D4B39EF),
            dense: false,
            controlAffinity: ListTileControlAffinity.trailing,
          );
        } else {
          return SizedBox();
        }
      },
    );
  }

  _buildConnectToFacebook() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return SwitchListTile.adaptive(
            value: state.isConnectToFacebook,
            onChanged: (newValue) async {
              context.read<UserBloc>().add(ConnectToFacebook(isConnectToFacebook: !state.isConnectToFacebook));
            },
            title: Row(
              children: [
                Text(
                  'เชื่อมต่อ Facebook',
                  style: FlutterFlowTheme.of(context).subtitle2,
                ),
                SizedBox(width: 16),
                Visibility(
                  visible: state.isConnectToFacebook,
                  child: Text('[เชื่อมต่อแล้ว]', style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: Color(0xFF4B39EF))),
                ),
              ],
            ),
            tileColor: Colors.white,
            activeColor: Color(0xFF4B39EF),
            activeTrackColor: Color(0x8D4B39EF),
            dense: false,
            controlAffinity: ListTileControlAffinity.trailing,
          );
        } else {
          return SizedBox();
        }
      },
    );
  }

  _buildConnectToGoogleBtn() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return SwitchListTile.adaptive(
            value: state.isConnectToGoogle,
            onChanged: (newValue) async {
              context.read<UserBloc>().add(ConnectToGoogle(isConnectToGoogle: !state.isConnectToGoogle));
            },
            title: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'เชื่อมต่อ Google',
                  style: FlutterFlowTheme.of(context).subtitle2,
                ),
                SizedBox(width: 16),
                Visibility(
                  visible: state.isConnectToGoogle,
                  child: Text('[เชื่อมต่อแล้ว]', style: FlutterFlowTheme.of(context).subtitle2.copyWith(color: Color(0xFF4B39EF))),
                ),
              ],
            ),
            tileColor: Colors.white,
            activeColor: Color(0xFF4B39EF),
            activeTrackColor: Color(0x8D4B39EF),
            dense: false,
            controlAffinity: ListTileControlAffinity.trailing,
          );
        } else {
          return SizedBox();
        }
      },
    );
  }

  _buildDeleteAccount(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return GestureDetector(
            onTap: () {
              // _modalBottomSheetRepository.showDeleteAccountDialog(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => DeleteAccountPage(
                            profile: profile ?? '',
                            name: nameController?.text ?? '',
                          )));
            },
            child: Container(
              padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              margin: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.red,
              ),
              child: Center(
                child: Text(
                  'ลบบัญชีผู้ใช้',
                  style: FlutterFlowTheme.of(context).subtitle2.override(
                        fontFamily: 'Sarabun',
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        useGoogleFonts: false,
                      ),
                ),
              ),
            ),
          );
        } else {
          return SizedBox();
        }
      },
    );
  }

  void _showImagePicker(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: Text('เลือกรูปภาพ'),
        actions: <Widget>[
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              chooseImage(0, context);
            },
            child: Text('แกลเลอรี่'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              chooseImage(1, context);
            },
            child: Text('ถ่ายรูป'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text('ยกเลิก'),
        ),
      ),
    );
  }

  void updateProfile(ApiCallResponse columnGetProfileResponse) async {
    var shouldSetState = false;
    ApiCallResponse updateProfileResponse = await UpdateProfileCall.call(
        token: FFAppState().token,
        email: GetProfileCall.email(
          columnGetProfileResponse.jsonBody,
        ).toString(),
        name: nameController!.text,
        avatar: GetProfileCall.avatar(
          columnGetProfileResponse.jsonBody,
        ).toString(),
        username: userNameController!.text);
    shouldSetState = true;

    if (!UpdateProfileCall.status(
      (updateProfileResponse.jsonBody ?? ''),
    )) {
      showError(UpdateProfileCall.msg((updateProfileResponse.jsonBody ?? '')));
      return;
    } else {
      showError(UpdateProfileCall.msg((updateProfileResponse.jsonBody ?? '')));
    }
    setState(() {
      Navigator.pop(context);
    });
  }

  Future<void> repass() async {
    var shouldSetState = false;
    ApiCallResponse updateProfileResponse =
        await RepassWordCall.call(token: FFAppState().token, oldPassword: oldPassController!.text, password: newPassController!.text, cfPassword: newPassController!.text);
    shouldSetState = true;

    if (!RepassWordCall.status(
      (updateProfileResponse.jsonBody ?? ''),
    )) {
      showError(RepassWordCall.message((updateProfileResponse.jsonBody ?? '')).toString());
      return;
    } else {
      showError(RepassWordCall.message((updateProfileResponse.jsonBody ?? '')).toString());
    }
    setState(() {
      Navigator.pop(context);
    });
  }
}
 */

// reset_password_confirm_page.dart
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import '../../config/shopchill_loading/shopchill_loading.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import 'reset_password_success_page.dart';

class ResetPasswordConfirmPage extends StatefulWidget {
  const ResetPasswordConfirmPage({super.key, this.mobile, this.resetToken});

  final String? mobile;
  final String? resetToken;

  @override
  State<ResetPasswordConfirmPage> createState() => _ResetPasswordConfirmPageState();
}

class _ResetPasswordConfirmPageState extends State<ResetPasswordConfirmPage> {
  late ApiCallResponse setPasswordResponse;

  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmNewPasswordController = TextEditingController();

  bool newPasswordVisible = false;
  bool confirmNewPasswordVisible = false;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void dispose() {
    newPasswordController.dispose();
    confirmNewPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        appBar: _buildAppBar(context),
        bottomNavigationBar: _buildSubmitButton(context),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(10),
            child: Column(
              children: [
                20.gap,
                _buildShopChillLogo(),
                10.gap,
                Center(child: Text('รหัสผ่านใหม่ต้องไม่ซ้ำกับรหัสก่อนหน้า', style: Theme.of(context).textTheme.bodyMedium)),
                10.gap,

                // New password
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text('รหัสผ่านใหม่', style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black)),
                ),
                8.gap,
                TextFormField(
                  controller: newPasswordController,
                  obscureText: !newPasswordVisible,
                  decoration: _passwordInputDecoration(
                    context,
                    onToggle: () => setState(() => newPasswordVisible = !newPasswordVisible),
                    isVisible: newPasswordVisible,
                  ),
                  style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 16),
                ),

                // Confirm new password
                16.gap,
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text('ยืนยันรหัสผ่านใหม่', style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black)),
                ),
                8.gap,
                TextFormField(
                  controller: confirmNewPasswordController,
                  obscureText: !confirmNewPasswordVisible,
                  decoration: _passwordInputDecoration(
                    context,
                    onToggle: () => setState(() => confirmNewPasswordVisible = !confirmNewPasswordVisible),
                    isVisible: confirmNewPasswordVisible,
                  ),
                  style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  InputDecoration _passwordInputDecoration(BuildContext context, {required VoidCallback onToggle, required bool isVisible}) {
    return InputDecoration(
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
        borderRadius: BorderRadius.circular(14),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
        borderRadius: BorderRadius.circular(14),
      ),
      filled: true,
      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
      contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
      suffixIcon: InkWell(
        onTap: onToggle,
        focusNode: FocusNode(skipTraversal: true),
        child: Icon(isVisible ? Icons.visibility_outlined : Icons.visibility_off_outlined, size: 22, color: const Color(0xFF757575)),
      ),
    );
  }

  void _onSubmitChangePassword() async {
    final newPw = newPasswordController.text;
    final confirmPw = confirmNewPasswordController.text;

    if (newPw.length < 8) {
      ShopChillLoading.showError('รหัสผ่านต้องยาวอย่างน้อย 8 ตัวอักษร');
      return;
    }
    if (newPw != confirmPw) {
      ShopChillLoading.showError('รหัสผ่านไม่ตรงกัน');
      return;
    }

    ShopChillLoading.show();
    setPasswordResponse = await SetNewPasswordCall.call(mobile: widget.mobile!, resetToken: widget.resetToken!, password: confirmPw);
    ShopChillLoading.dismiss();

    if (SetNewPasswordCall.status(setPasswordResponse.jsonBody ?? '')) {
      if (!mounted) return;
      await Navigator.pushReplacement(context, CupertinoPageRoute(builder: (_) => const ResetPasswordSuccessPage()));
    } else {
      ShopChillLoading.showError(SetNewPasswordCall.msg(setPasswordResponse.jsonBody ?? ''));
    }
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('ตั้งรหัสผ่านใหม่', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),
      centerTitle: true,
      elevation: 0,
    );
  }

  Widget _buildShopChillLogo() => Image.asset(UImageAssets.SHOPCHILL_LOGO, width: 200);

  Widget _buildSubmitButton(BuildContext context) {
    return Container(
      height: 54,
      margin: const EdgeInsets.only(bottom: 30, left: 10, right: 10),
      decoration: BoxDecoration(color: ColorThemeConfig.newPrimaryColor, borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: _onSubmitChangePassword,
        child: Center(
          child: Text(
            'ยืนยัน',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
          ),
        ),
      ),
    );
  }
}


/* [old]
import 'package:flutter/cupertino.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import '../../config/shopchill_loading/shopchill_loading.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import 'package:flutter/material.dart';
import '../forgotpassword_four/forgotpassword_four_widget.dart';

class ForgotpasswordThreeWidget extends StatefulWidget {
  const ForgotpasswordThreeWidget({super.key, this.mobile, this.resettoken});

  final String? mobile;
  final String? resettoken;

  @override
  _ForgotpasswordThreeWidgetState createState() => _ForgotpasswordThreeWidgetState();
}

class _ForgotpasswordThreeWidgetState extends State<ForgotpasswordThreeWidget> {
  late ApiCallResponse setpasswordResponse;
  TextEditingController? confirmPasswordController;
  late bool confirmPasswordVisibility;
  TextEditingController? passwordController;
  late bool passwordVisibility;
  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    confirmPasswordController = TextEditingController();
    confirmPasswordVisibility = false;
    passwordController = TextEditingController();
    passwordVisibility = false;
  }

  @override
  Widget build(BuildContext context) {
/*     var pageSize = MediaQuery.of(context).size.height;
    var notifySize = MediaQuery.of(context).padding.top;
    var appBarSize = AppBar().preferredSize.height; */

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),

      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        appBar: _buildAppBar(context),
        bottomNavigationBar: _buildSubmitButton(context),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  20.gap,
                  _buildShopChillLogo(),
                  10.gap,

                  Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(child: Text('รหัสผ่านใหม่ต้องไม่ซ้ำกับรหัสก่อนหน้า', style: Theme.of(context).textTheme.bodyMedium)),
                      10.gap,
                      Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'รหัสผ่านใหม่',
                                  style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                  child: Container(
                                    width: double.infinity,
                                    decoration: const BoxDecoration(),
                                    child: TextFormField(
                                      controller: passwordController,
                                      obscureText: !passwordVisibility,
                                      decoration: InputDecoration(
                                        labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                        hintText: '',
                                        hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                        enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                          borderRadius: BorderRadius.circular(14),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                          borderRadius: BorderRadius.circular(14),
                                        ),
                                        filled: true,
                                        fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                        contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                        suffixIcon: InkWell(
                                          onTap: () => setState(() => passwordVisibility = !passwordVisibility),
                                          focusNode: FocusNode(skipTraversal: true),
                                          child: Icon(passwordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined, color: const Color(0xFF757575), size: 22),
                                        ),
                                      ),
                                      style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'ยืนยันรหัสผ่านใหม่',
                                    style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                  ),
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                    child: Container(
                                      width: double.infinity,
                                      decoration: const BoxDecoration(),
                                      child: TextFormField(
                                        controller: confirmPasswordController,
                                        obscureText: !confirmPasswordVisibility,
                                        decoration: InputDecoration(
                                          labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                          hintText: '',
                                          hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                          enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                            borderRadius: BorderRadius.circular(14),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 2),
                                            borderRadius: BorderRadius.circular(14),
                                          ),
                                          filled: true,
                                          fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                          contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                          suffixIcon: InkWell(
                                            onTap: () => setState(() => confirmPasswordVisibility = !confirmPasswordVisibility),
                                            focusNode: FocusNode(skipTraversal: true),
                                            child: Icon(
                                              confirmPasswordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                                              color: const Color(0xFF757575),
                                              size: 22,
                                            ),
                                          ),
                                        ),
                                        style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onSubmitChangePassword() async {
    var shouldSetState = false;
    if (passwordController!.text == confirmPasswordController!.text) {
      ShopChillLoading.show();
      setpasswordResponse = await SetNewPasswordCall.call(mobile: widget.mobile!, resetToken: widget.resettoken!, password: confirmPasswordController!.text);
      ShopChillLoading.dismiss();
      shouldSetState = true;
      if (SetNewPasswordCall.status((setpasswordResponse.jsonBody ?? ''))) {
        await Navigator.push(context, CupertinoPageRoute(builder: (context) => const ForgotpasswordFourWidget()));

        if (shouldSetState) setState(() {});
        return;
      } else {
        ShopChillLoading.showError(SetNewPasswordCall.msg((setpasswordResponse.jsonBody ?? '')));
        if (shouldSetState) setState(() {});
        return;
      }
    } else if (passwordController!.text.length < 8) {
      ShopChillLoading.showError('รหัสผ่านต้องยาวอย่างน้อย 8 ตัวอักษร');
    } else {
      ShopChillLoading.showError('รหัสผ่านไม่ตรงกัน');
      return;
    }
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('ตั้งรหัสผ่านใหม่', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),
      centerTitle: true,
      elevation: 0,
    );
  }

  Widget _buildShopChillLogo() {
    return Image.asset(UImageAssets.SHOPCHILL_LOGO, width: 200);
  }

  Widget _buildSubmitButton(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 54,
      margin: const EdgeInsets.only(bottom: 30, left: 10, right: 10),

      padding: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(color: ColorThemeConfig.newPrimaryColor, borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: _onSubmitChangePassword,
        child: Center(
          child: Text(
            'ยืนยัน',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
          ),
        ),
      ),
    );
  }
}
 */
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/login/cubit/timer_cubit.dart';
import 'package:shop_chill_app/screens/login/cubit/timer_state.dart';
import 'package:shop_chill_app/screens/login/otp_confirmation.dart';
import 'package:shop_chill_app/screens/login/widgets/phone_text_form_field.dart';
import 'package:shop_chill_app/screens/login/widgets/submit_button.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_bg.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_logo.dart';

import '../../config/shopchill_loading/shopchill_loading.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import 'forgot_password_otp_confirmation.dart';
import 'package:flutter/material.dart';

class ForgotPasswordRequestPage extends StatefulWidget {
  const ForgotPasswordRequestPage({super.key});

  @override
  _ForgotPasswordRequestPagetState createState() => _ForgotPasswordRequestPagetState();
}

class _ForgotPasswordRequestPagetState extends State<ForgotPasswordRequestPage> {
  late ApiCallResponse resetpasswordResponse;
  TextEditingController mobileController = TextEditingController();

  final ValueNotifier<bool> isEnabledValue = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    mobileController.addListener(_validateInput);
  }

  void _validateInput() {
    final isCountdown = context.read<WorkoutCubit>().state is WorkoutInProgress;
    final isValid = mobileController.text.isNotEmpty && mobileController.text.length == 10 && !isCountdown;
    isEnabledValue.value = isValid;
  }

  @override
  void dispose() {
    mobileController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: _buildAppBar(context),
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        body: ShopchillBg(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const ShopchillLogo(isHorizontal: true),
              20.gap,
              _buildInputPhoneNumberSection(context),
              const Spacer(),
              _buildSendOTPButtonSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSendOTPButtonSection() {
    return BlocConsumer<WorkoutCubit, WorkoutState>(
      listener: (context, state) {
        _validateInput();
      },
      builder: (context, state) {
        return SubmitButton(
          isEnabled: isEnabledValue,
          onTap: () async => _resetPasswordProcess(),
          label: state is WorkoutInProgress ? 'ส่งได้อีกครั้งในอีก ${state.elapsed}' : 'ขอรหัส OTP',
        );
      },
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => Navigator.pop(context, false),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('ลืมรหัสผ่าน', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),
      centerTitle: true,
      elevation: 0,
    );
  }

  Widget _buildInputPhoneNumberSection(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('หมายเลขโทรศัพท์', style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black)),
        10.gap,
        PhoneTextFormField(phoneController: mobileController, havePrefixIcon: false),
      ],
    );
  }

  void _resetPasswordProcess() async {
    if (mobileController.text.isEmpty) {
      ShopChillLoading.showError("กรุณาระบุหมายเลขโทรศัพท์");

      return;
    }
    ShopChillLoading.show();
    try {
      resetpasswordResponse = await ResetPasswordCall.call(mobile: mobileController.text);
      ShopChillLoading.dismiss();

      if (ResetPasswordCall.status((resetpasswordResponse.jsonBody ?? ''))) {
        BlocProvider.of<WorkoutCubit>(context).startWorkout(0);
        await Navigator.pushReplacement(
          context,
          CupertinoPageRoute(
            builder: (context) => OtpConfirmation(
              mobile: mobileController.text,
              type: ConfirmationOtpType.forgotPassword,
              refCode: ResetPasswordCall.refcode((resetpasswordResponse.jsonBody ?? '')),
            ),
          ),
        );
        return;
      } else {
        ShopChillLoading.dismiss();

        ShopChillLoading.showError(ResetPasswordCall.msg((resetpasswordResponse.jsonBody ?? '')));
        return;
      }
    } finally {
      ShopChillLoading.dismiss();
    }
  }
}

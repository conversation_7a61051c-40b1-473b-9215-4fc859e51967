import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/home/<USER>/home_bloc.dart';
import 'package:shop_chill_app/screens/login/widgets/submit_button.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/order/bloc/order_count_cubit/order_count_cubit.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_logo.dart';

import '../../config/shopchill_loading/shopchill_loading.dart';

class RegisterFormPage extends StatefulWidget {
  final String phone;
  final bool isProviderLogin;
  const RegisterFormPage({super.key, required this.phone, required this.isProviderLogin});

  @override
  State<RegisterFormPage> createState() => _RegisterFormPageState();
}

class _RegisterFormPageState extends State<RegisterFormPage> {
  final formKey = GlobalKey<FormState>();
  TextEditingController userNameController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController emailAddressController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  late bool passwordVisibility;
  late bool confirmPasswordVisibility;

  final ValueNotifier<bool> isRegisterEnabled = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    phoneController.text = widget.phone;
    passwordVisibility = false;
    confirmPasswordVisibility = false;
  }

  @override
  void dispose() {
    confirmPasswordController.dispose();
    emailAddressController.dispose();
    phoneController.dispose();
    userNameController.dispose();
    nameController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  void _validateInput() {
    final isValid = phoneController.text.isNotEmpty && phoneController.text.length == 10;
    isRegisterEnabled.value = isValid;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: _buildAppBar(context),
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        body: SafeArea(
          child: SingleChildScrollView(
            child: Form(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  20.gap,
                  const ShopchillLogo(isHorizontal: true),
                  20.gap,
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'กรอกชื่อผู้ใช้งาน',
                                          style: FlutterFlowTheme.of(
                                            context,
                                          ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                        ),
                                        Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                          child: Container(
                                            width: double.infinity,
                                            decoration: const BoxDecoration(),
                                            child: TextFormField(
                                              validator: (value) {
                                                if (value!.isEmpty) {
                                                  return 'กรุณากรอก username';
                                                } else {
                                                  return null;
                                                }
                                              },
                                              controller: userNameController,
                                              obscureText: false,
                                              decoration: _cuttomDecoration(context, 'username'),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'ชื่อ-นามสกุล',
                                          style: FlutterFlowTheme.of(
                                            context,
                                          ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                        ),
                                        Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                          child: Container(
                                            width: double.infinity,
                                            decoration: const BoxDecoration(),
                                            child: TextFormField(
                                              validator: (value) {
                                                if (value!.isEmpty) {
                                                  return 'กรุณากรอก ชื่อ-นามสกุล';
                                                } else {
                                                  return null;
                                                }
                                              },
                                              controller: nameController,
                                              obscureText: false,
                                              decoration: _cuttomDecoration(context, 'ชื่อ-นามสกุล'),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'หมายเลขโทรศัพท์',
                                          style: FlutterFlowTheme.of(
                                            context,
                                          ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                        ),
                                        Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                          child: Container(
                                            width: double.infinity,
                                            decoration: const BoxDecoration(),
                                            child: TextFormField(
                                              readOnly: true,
                                              enableInteractiveSelection: false,
                                              controller: phoneController,
                                              obscureText: false,
                                              maxLength: 10,
                                              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                                              validator: (value) {
                                                if (value!.isEmpty) {
                                                  return 'กรุณากรอก หมายเลขโทรศัพท์';
                                                } else {
                                                  return null;
                                                }
                                              },
                                              decoration: _cuttomDecoration(context, 'หมายเลขโทรศัพท์'),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                              keyboardType: TextInputType.number,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'E-mail',
                                          style: FlutterFlowTheme.of(
                                            context,
                                          ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                        ),
                                        Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                          child: Container(
                                            width: double.infinity,
                                            decoration: const BoxDecoration(),
                                            child: TextFormField(
                                              validator: (value) {
                                                final RegExp emailRegex = RegExp(r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$");

                                                if (value == null || value.isEmpty) {
                                                  return 'กรุณากรอก e-mail';
                                                } else if (!emailRegex.hasMatch(value)) {
                                                  return 'รูปแบบ e-mail ไม่ถูกต้อง';
                                                } else {
                                                  return null;
                                                }
                                              },
                                              controller: emailAddressController,
                                              obscureText: false,
                                              decoration: _cuttomDecoration(context, 'E-mail'),
                                              style: Theme.of(context).textTheme.bodyMedium,

                                              keyboardType: TextInputType.emailAddress,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'รหัสผ่าน',
                                          style: FlutterFlowTheme.of(
                                            context,
                                          ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                        ),
                                        Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                          child: Container(
                                            width: double.infinity,
                                            decoration: const BoxDecoration(),
                                            child: TextFormField(
                                              validator: (value) {
                                                if (value == null || value.isEmpty) {
                                                  return 'กรุณากรอกรหัสผ่าน';
                                                } else if (value.length < 8) {
                                                  return 'รหัสผ่านต้องมีความยาวอย่างน้อย 8 ตัว';
                                                } else {
                                                  return null;
                                                }
                                              },
                                              controller: passwordController,
                                              obscureText: !passwordVisibility,
                                              decoration: _cuttomDecoration(
                                                context,
                                                'รหัสผ่าน',
                                                suffixIcon: InkWell(
                                                  onTap: () => setState(() => passwordVisibility = !passwordVisibility),
                                                  focusNode: FocusNode(skipTraversal: true),
                                                  child: Icon(
                                                    passwordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                                                    color: const Color(0xFF757575),
                                                    size: 22,
                                                  ),
                                                ),
                                              ),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'ยืนยันรหัสผ่าน',
                                          style: FlutterFlowTheme.of(
                                            context,
                                          ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, useGoogleFonts: false),
                                        ),
                                        Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                          child: Container(
                                            width: double.infinity,
                                            decoration: const BoxDecoration(),
                                            child: TextFormField(
                                              validator: (value) {
                                                if (value == null || value.isEmpty) {
                                                  return 'กรุณากรอกยืนยันรหัสผ่าน';
                                                } else if (value != passwordController.text) {
                                                  return 'ยืนยันรหัสผ่านไม่ตรงกับรหัสผ่านที่คุณป้อน';
                                                } else {
                                                  return null;
                                                }
                                              },
                                              controller: confirmPasswordController,
                                              obscureText: !confirmPasswordVisibility,
                                              decoration: _cuttomDecoration(
                                                context,
                                                'ยืนยันรหัสผ่าน',
                                                suffixIcon: InkWell(
                                                  onTap: () => setState(() => confirmPasswordVisibility = !confirmPasswordVisibility),
                                                  focusNode: FocusNode(skipTraversal: true),
                                                  child: Icon(
                                                    confirmPasswordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                                                    color: const Color(0xFF757575),
                                                    size: 22,
                                                  ),
                                                ),
                                              ),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 20),
                            _buildRegisterButtonSection(context),
                            const SizedBox(height: 30),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('สมัครสมาชิกใหม่', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),
      centerTitle: true,
      elevation: 0,
    );
  }

  InputDecoration _cuttomDecoration(BuildContext context, String labelText, {Widget? suffixIcon}) {
    return InputDecoration(
      labelStyle: FlutterFlowTheme.of(context).bodyText1,
      labelText: labelText,
      counterText: '',
      floatingLabelBehavior: FloatingLabelBehavior.never,
      hintStyle: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.grey),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 1),
        borderRadius: BorderRadius.circular(14),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryColor, width: 1),
        borderRadius: BorderRadius.circular(14),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Colors.red, width: 1),
        borderRadius: BorderRadius.circular(14),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Colors.red, width: 1),
        borderRadius: BorderRadius.circular(14),
      ),
      filled: true,
      fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
      contentPadding: const EdgeInsetsDirectional.all(10),

      suffixIcon: suffixIcon,
    );
  }

  Widget _buildRegisterButtonSection(BuildContext context) {
    return SubmitButton(isEnabled: isRegisterEnabled, onTap: _onSubmitRegister, label: 'สมัครสมาชิก');
  }
  /* Widget _buildRegisterButtonSection(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 54,
      padding: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(color: ColorThemeConfig.newPrimaryColor, borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: _onSubmitRegister,
        child: Center(
          child: Text(
            'สมัครสมาชิก',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
          ),
        ),
      ),
    );
  } */

  void _onSubmitRegister() async {
    if (formKey.currentState!.validate()) {
      FocusScope.of(context).unfocus();
      await Future.delayed(const Duration(milliseconds: 100));
      ShopChillLoading.show();
      try {
        final res = await RegisterCall().register(
          name: nameController.text,
          username: userNameController.text,
          email: emailAddressController.text,
          phone: phoneController.text,
          password: passwordController.text,
          confirmPassword: confirmPasswordController.text,
        );

        ShopChillLoading.dismiss();

        if (res['status'] == true) {
          FFAppState().token = res['data']['api_token'];

          context.read<UserBloc>().add(const GetUserProfile());
          context.read<CartBloc>().add(GetCartEvent());
          context.read<OrderCountCubit>().fetchOrderCount();
          context.read<HomeBloc>().add(GetHomeEvent(context));

          Navigator.of(context).popUntil((route) => route.isFirst);

          return;
        } else {
          showError(res['message'] ?? res['msg'] ?? 'เกิดข้อผิดพลาดกรุณาลองใหม่');
          return;
        }
      } catch (e, st) {
        debugPrintStack(label: 'Error: $e', stackTrace: st);
        showError('เกิดข้อผิดพลาดกรุณาลองใหม่');
      } finally {
        ShopChillLoading.dismiss();
      }
    }
  }
}

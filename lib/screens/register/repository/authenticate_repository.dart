import 'package:flutter/cupertino.dart';
import 'package:shop_chill_app/backend/api_requests/api_end_point_v2.dart';
import 'package:shop_chill_app/backend/api_requests/api_manager_v2.dart';
import 'package:shop_chill_app/screens/register/models/provider_auth_request.dart';
import 'package:shop_chill_app/screens/register/models/provider_auth_response.dart';
import 'package:shop_chill_app/screens/register/models/register_request.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';

abstract class BaseAuthenticateRepository {
  Future<ProviderAuthResponse?> register({required ProviderAuthRequest request, required ProviderType provider});
  Future<ProviderAuthResponse?> registerAuth({required RegisterRequest request});
}

class AuthenticateRepository implements BaseAuthenticateRepository {
  final ApiManagerV2 _apiManager = ApiManagerV2();

  @override
  Future<ProviderAuthResponse?> register({required ProviderAuthRequest request, required ProviderType provider}) async {
    try {
      final response = await _apiManager.post(ApiEndPointV2.PROVIDER_AUTH.replaceAll('{provider}', provider.name), request.toJson());

      return ProviderAuthResponse.fromJson(response);
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
      return null;
    }
  }

  @override
  Future<ProviderAuthResponse?> registerAuth({required RegisterRequest request}) async {
    try {
      final response = await _apiManager.post(ApiEndPointV2.PROVIDER_REGISTER, request.toJson());

      return ProviderAuthResponse.fromJson(response);
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
      return null;
    }
  }
}

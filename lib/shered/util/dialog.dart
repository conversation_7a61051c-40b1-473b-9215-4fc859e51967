import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/checkout/widgets/my_address_widget.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/mall/bloc/mall_bloc.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/my_account/models/user_model.dart';
import 'package:shop_chill_app/screens/my_account/repository/user_repository.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/shop/bloc/shop_bloc.dart';
import 'package:shop_chill_app/shered/util/utils.dart';
import 'package:shop_chill_app/shered/widgets/dialog/custom_alert_dialog.dart';

import '../../auth/auth_util.dart';
import '../../config/shopchill_loading/shopchill_loading.dart';

class ShopChillDialogs {
  final UserRepository _userRepository = UserRepository();

  void showDialogSureToUnfollow(BuildContext context, String shopId) {
    final AlertDialog alert = AlertDialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      backgroundColor: Colors.white.withValues(alpha: 0.9),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Center(
        child: Text(
          'เลิกติดตาม',
          style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black, fontSize: 18, fontWeight: FontWeight.w600),
        ),
      ),
      titleTextStyle: Theme.of(context).textTheme.titleLarge,
      titlePadding: const EdgeInsets.only(left: 20, top: 20, bottom: 5),
      contentPadding: EdgeInsets.zero,
      actions: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: Colors.grey,
                  child: Text(
                    'ยกเลิก',
                    style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
                  ),
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: ColorThemeConfig.primaryColor,
                  child: Text(
                    'ตกลง',
                    style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
                  ),
                  onPressed: () async {
                    context.read<ShopBloc>().add(FollowShopEvent(int.parse(shopId)));
                    context.read<MallBloc>().add(UpdateFollower(shopId: shopId));
                    context.read<CartBloc>().add(CartUpdateFollower(shopId: shopId));
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                ),
              ),
            ],
          ),
        ),
      ],
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                'หากคุณยกเลิกการติดตามบัญชีนี้ คุณจะไม่ได้รับอัปเดตอีกต่อไป คุณยังจะต้องการทำรายการต่อหรือไม่?',
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w500),
              ),
            ),
            //context.read<ChatBloc>().add(CreateGroupMultiUserIdEvent());
          ],
        ),
      ),
    );
    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  /*  showDialogSureToLogOut(BuildContext context, UserModel user) {
    AlertDialog alert = AlertDialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      backgroundColor: Colors.white.withOpacity(.9),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Center(
          child: Text(
        'ออกจากระบบ',
        style: FlutterFlowTheme.of(context).bodyText1.override(
              fontFamily: 'Sarabun',
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.w500,
              useGoogleFonts: false,
            ),
      )),
      titleTextStyle: Theme.of(context).textTheme.titleLarge,
      titlePadding: const EdgeInsets.only(left: 20, top: 20, bottom: 5),
      contentPadding: EdgeInsets.zero,
      actions: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: Colors.grey,
                  child: Text(
                    'ยกเลิก',
                    style: FlutterFlowTheme.of(context).bodyText1.override(
                          fontFamily: 'Sarabun',
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          useGoogleFonts: false,
                        ),
                  ),
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: Color(0xFF0056D6),
                  child: Text(
                    'ตกลง',
                    style: FlutterFlowTheme.of(context).bodyText1.override(
                          fontFamily: 'Sarabun',
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          useGoogleFonts: false,
                        ),
                  ),
                  onPressed: () async {
                    Navigator.of(context, rootNavigator: true).pop();
                    showLoading();
                    await signOut();
                    FFAppState().token = "";
                    FFAppState().uid = '';
                    await FacebookAuth.instance.logOut();
                    await FirebaseAuth.instance.signOut();
                    context.read<UserBloc>().add(RefreshUserEvent());
                    // await context.read<UserBloc>().stream.first;
                    hideLoading();
                  },
                ),
              ),
            ],
          ),
        )
      ],
    );
    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  } */
  void showDialogSureToLogOut(BuildContext context, UserModel user) {
    showDialog(
      context: context,
      builder: (BuildContext dContext) {
        return CustomAlertDialog(
          title: 'ออกจากระบบ',
          description: 'ยืนยันออกจากระบบ?',
          onCancel: () => Navigator.of(context, rootNavigator: true).pop(),
          onConfirm: () async {
            Navigator.of(dContext).pop();

            return Utils().onLogoutProcess(context, routeIsFirst: true);
          },
        );
      },
    );
  }

  void showDeleteAccountDialog(BuildContext context) {
    final AlertDialog alert = AlertDialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      backgroundColor: Colors.white.withOpacity(.9),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Center(
        child: Text(
          'ลบบัญชีผู้ใช้',
          style: FlutterFlowTheme.of(
            context,
          ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.black, fontSize: 18, fontWeight: FontWeight.w500, useGoogleFonts: false),
        ),
      ),
      titleTextStyle: Theme.of(context).textTheme.titleLarge,
      titlePadding: const EdgeInsets.only(left: 20, top: 20, bottom: 5),
      actionsPadding: const EdgeInsets.only(right: 20),
      contentPadding: EdgeInsets.zero,
      actions: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: Colors.grey,
                  child: Text(
                    'ยกเลิก',
                    style: FlutterFlowTheme.of(context).bodyText1.override(
                      fontFamily: 'Sarabun',
                      color: Colors.white,
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      useGoogleFonts: false,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: Colors.red,
                  child: Text(
                    'ตกลง',
                    style: FlutterFlowTheme.of(context).bodyText1.override(
                      fontFamily: 'Sarabun',
                      color: Colors.white,
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      useGoogleFonts: false,
                    ),
                  ),
                  onPressed: () async {
                    Navigator.of(context, rootNavigator: true).pop();
                    showLoading();
                    // Perform delete account logic here
                    Navigator.pop(context); // Close the dialog after action
                    final res = await _userRepository.deleteAccount();
                    ShopChillLoading.show(status: res['msg']);
                    await signOut();
                    FFAppState().token = "";
                    context.read<UserBloc>().add(const RefreshUserEvent());
                    hideLoading();
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  void showDialogAddPrimaryAddress(BuildContext context, {VoidCallback? callback}) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CustomAlertDialog(
          title: 'เพิ่มที่อยู่สำหรับจัดส่ง',
          description: 'บัญชีของคุณไม่มีที่อยู่จัดส่ง กรุณาเพิ่มที่อยู่เพื่อดำเนินการชำระเงิน',
          onCancel: () => Navigator.of(context, rootNavigator: true).pop(),
          btnCancelText: 'ไว้ภายหลัง',
          btnConfirmText: 'เพิ่มที่อยู่',
          onConfirm: () async {
            Navigator.of(dialogContext).pop();
            await addOrEditAddressProcess(context, isEditAddress: false).then((v) {
              callback?.call();
            });
          },
        );
      },
    );
  }

  void showReactivationAccountDialog(BuildContext context, {VoidCallback? callback}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return CustomAlertDialog(
          title: 'เปิดใช้งานบัญชีอีกครั้ง?',
          description: 'บัญชีของคุณถูกปิดการใช้งาน หากคุณเข้าสู่ระบบตอนนี้ บัญชีของคุณจะได้รับการเปิดใช้งานอีกครั้ง',
          onCancel: () => Navigator.of(dialogContext, rootNavigator: true).pop(),
          btnCancelText: 'ยกเลิก',
          btnConfirmText: 'ยืนยัน',
          onConfirm: () => callback?.call(),
        );
      },
    );
  }
}

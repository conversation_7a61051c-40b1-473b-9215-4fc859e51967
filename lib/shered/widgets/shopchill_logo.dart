import 'package:flutter/material.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';

class ShopchillLogo extends StatelessWidget {
  final bool isHorizontal;
  final VoidCallback? onLongPress;
  const ShopchillLogo({super.key, this.isHorizontal = false, this.onLongPress});

  @override
  Widget build(BuildContext context) => Center(
    child: GestureDetector(
      onLongPress: onLongPress,
      
      child: Image.asset(
        !isHorizontal ? UImageAssets.SHOPCHILL_LOGO : UImageAssets.SHOPCHILL_LOGO_HORIZONTAL,
        width: !isHorizontal ? 160 : MediaQuery.of(context).size.width / 1.5,
      ),
    ),
  );
}

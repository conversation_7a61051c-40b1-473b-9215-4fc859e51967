import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:isar/isar.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/auth/auth_util.dart';
import 'package:shop_chill_app/backend/schema/local_database/local_database.dart';
import 'package:shop_chill_app/backend/schema/local_database/shopchill_database.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/nav_bar/nav_bar_page.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';
import 'package:shop_chill_app/shered/util/custom_text.dart';
import 'package:shop_chill_app/shered/util/modal.dart';

class DeleteDetailPage extends StatefulWidget {
  final String name;
  final String profile;
  final String uid;
  final String email;
  final ProviderType platform;

  const DeleteDetailPage({super.key, required this.name, required this.profile, required this.uid, required this.email, required this.platform});

  @override
  State<DeleteDetailPage> createState() => _DeleteDetailPageState();
}

class _DeleteDetailPageState extends State<DeleteDetailPage> {
  final ShopChillDatabase localDatabase = ShopChillDatabase();

  Isar? db;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    openDB();
  }

  void openDB() async {
    final Isar isar = await localDatabase.openDB();
    setState(() {
      db = isar;
    });
  }

  Future<void> _deleteFacebookAccount() async {
    final facebookProfile = FacebookProfile()
      ..name = widget.name
      ..profile = widget.profile
      ..uid = widget.uid
      ..email = widget.email;

    await db!.writeTxn(() async {
      await db!.facebookProfiles.put(facebookProfile);
    });

    showLoading();
    await signOut();
    FFAppState().token = "";
    FFAppState().uid = '';
    await FacebookAuth.instance.logOut();
    await FirebaseAuth.instance.signOut();
    context.read<UserBloc>().add(RefreshUserEvent());
    // await context.read<UserBloc>().stream.first;
    hideLoading();

    Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const NavBarPage(initialPage: 0)), (route) => false);
  }

  Future<void> _deleteGoogleAccount() async {
    final googleProfile = GoogleProfile()
      ..name = widget.name
      ..profile = widget.profile
      ..uid = widget.uid
      ..email = widget.email;

    await db!.writeTxn(() async {
      await db!.googleProfiles.put(googleProfile);
    });

    showLoading();
    await signOut();
    FFAppState().token = "";
    FFAppState().uid = '';
    await FacebookAuth.instance.logOut();
    await FirebaseAuth.instance.signOut();
    context.read<UserBloc>().add(RefreshUserEvent());
    // await context.read<UserBloc>().stream.first;
    hideLoading();

    Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const NavBarPage(initialPage: 0)), (route) => false);
  }

  Future<void> _deleteAppleAccount() async {
    final appleProfile = AppleProfile()
      ..name = widget.name
      ..profile = widget.profile
      ..uid = widget.uid
      ..email = widget.email;

    await db!.writeTxn(() async {
      await db!.appleProfiles.put(appleProfile);
    });

    showLoading();
    await signOut();
    FFAppState().token = "";
    FFAppState().uid = '';
    await FacebookAuth.instance.logOut();
    await FirebaseAuth.instance.signOut();
    context.read<UserBloc>().add(RefreshUserEvent());
    // await context.read<UserBloc>().stream.first;
    hideLoading();

    Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const NavBarPage(initialPage: 0)), (route) => false);
  }

  Future<void> _deleteLineAccount() async {
    final lineProfile = LineProfile()
      ..name = widget.name
      ..profile = widget.profile
      ..uid = widget.uid
      ..email = widget.email;

     await db!.writeTxn(() async {
      await db!.lineProfiles.put(lineProfile);
    });

    showLoading();
    await signOut();
    FFAppState().token = "";
    FFAppState().uid = '';
    await FacebookAuth.instance.logOut();
    await FirebaseAuth.instance.signOut();
    context.read<UserBloc>().add(RefreshUserEvent());
    // await context.read<UserBloc>().stream.first;
    hideLoading();

    Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const NavBarPage(initialPage: 0)), (route) => false);
  }

  Future<void> _deletePhoneAccount() async {
    final phoneProfile = PhoneProfile()
      ..name = widget.name
      ..profile = widget.profile
      ..uid = widget.uid
      ..email = widget.email;

    await db!.writeTxn(() async {
      await db!.phoneProfiles.put(phoneProfile);
    });

    showLoading();
    await signOut();
    FFAppState().token = "";
    FFAppState().uid = '';
    await FacebookAuth.instance.logOut();
    await FirebaseAuth.instance.signOut();
    context.read<UserBloc>().add(RefreshUserEvent());
    // await context.read<UserBloc>().stream.first;
    hideLoading();

    Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const NavBarPage(initialPage: 0)), (route) => false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 24),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: const CustomText(text: 'สิ่งที่ต้องตรวจสอบเมื่อลบบัญชีของคุณ', color: Colors.black, fontWeight: FontWeight.bold, fontSize: 18),
        centerTitle: true,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDataWidget(title: 'คุณไม่สามารถเข้าสู่ระบบ ShopChill ด้วยบัญชีนี้หลังจากที่ลบไปแล้ว'),
            const SizedBox(height: 10),
            _buildDataWidget(title: 'คุณไม่สามารถลงทะเบียนบัญชีใหม่โดยใช้ที่อยู่อีเมลซึ่งเชื่อมโยงกับบัญชีนี้ไปแล้ว'),
            const SizedBox(height: 10),
            _buildDataWidget(title: ' บัญชีของคุณจะถูกลบอย่างถาวรใน 14 วัน คุณสามารถเปิดใช้งานอีกครั้งได้ตลอดเวลาภายในระยะเวลานี้'),
            const Spacer(),
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: CupertinoButton(
                color: FlutterFlowTheme.of(context).primaryColor,
                child: const CustomText(text: 'ยอมรับ', color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
                onPressed: () {
                  CustomDialog.showCustomDialog(
                    context: context,
                    title: 'ยืนยันลบบัญชีของคุณหรือไม่',
                    description: 'บัญชี (${widget.name}) จะถูกลบ คุณสามารถเปิดใช้งานอีกครั้งได้ตลอดเวลาภายใน 14 วัน',
                    primaryButtonText: 'ลบบัญชี',
                    primaryButtonOnPressed: () async {
                      switch (widget.platform) {
                        case ProviderType.google:
                          _deleteGoogleAccount();
                          break;
                        case ProviderType.facebook:
                          _deleteFacebookAccount();
                          break;
                        case ProviderType.apple:
                          _deleteAppleAccount();
                          break;

                        case ProviderType.line:
                          _deleteLineAccount();
                          break;
                        case ProviderType.others:
                          _deletePhoneAccount();
                          break;
                      }
                    },
                  );
                },
              ),
            ),
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }

  Widget _buildDataWidget({required String title}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Icon(Icons.circle, color: Colors.grey, size: 12),
        const SizedBox(width: 10),
        Expanded(
          child: CustomText(text: title, color: Colors.black, fontWeight: FontWeight.normal, fontSize: 16),
        ),
      ],
    );
  }
}

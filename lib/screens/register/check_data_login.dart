import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/notification/fcm/fcm_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/order/bloc/order_count_cubit/order_count_cubit.dart';
import 'package:shop_chill_app/screens/register/models/check_verify_model.dart';
import 'package:shop_chill_app/shered/widgets/profile_avatar.dart';

class CheckDataLogin extends StatefulWidget {
  final UserData userData;
  const CheckDataLogin({super.key, required this.userData});

  @override
  State<CheckDataLogin> createState() => _CheckDataLoginState();
}

class _CheckDataLoginState extends State<CheckDataLogin> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
      body: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12), color: Colors.white),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            const SizedBox(height: 20),
            ProfileAvatar(url: widget.userData.avatar ?? "", size: 100),
            const SizedBox(height: 20),
            Row(
              children: [
                const Text('คุณ : ', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('${widget.userData.username}'),
              ],
            ),
            Row(
              children: [
                const Text('e-mail : ', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('${widget.userData.email}'),
              ],
            ),
            Row(
              children: [
                const Text('เบอร์โทร : ', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('${widget.userData.phone}'),
              ],
            ),
            const SizedBox(height: 20),

            _buildLoginButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginButton(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 54,
      padding: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(color: ColorThemeConfig.newPrimaryColor, borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () async {
          final String? fcmToken = await getFcmToken();
          print('fcm = $fcmToken');
          setState(() => FFAppState().token = widget.userData.apiToken!);
          await StoreFCM.storefcm(fcm: fcmToken);

          context.read<UserBloc>().add(const GetUserProfile());
          context.read<CartBloc>().add(GetCartEvent());
          context.read<OrderCountCubit>().fetchOrderCount();

          Navigator.of(context).popUntil((route) => route.isFirst);
        },
        child: Center(
          child: Text(
            'เข้าสู่ระบบ',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('คุณได้สมัครสมาชิกแล้ว', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),
      centerTitle: true,
      elevation: 0,
    );
  }
}

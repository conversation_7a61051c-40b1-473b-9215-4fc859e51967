import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:isar/isar.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/auth/model/line_user_profile.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/backend/schema/local_database/local_database.dart';
import 'package:shop_chill_app/backend/schema/local_database/shopchill_database.dart';
import 'package:shop_chill_app/screens/chat/blocs/allchat_cubit/all_chat_room_cubit.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/home/<USER>/home_bloc.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/notification/fcm/fcm_bloc.dart';
import 'package:shop_chill_app/screens/order/bloc/order_count_cubit/order_count_cubit.dart';
import 'package:shop_chill_app/screens/register/models/provider_auth_request.dart';
import 'package:shop_chill_app/screens/register/models/provider_auth_response.dart';
import 'package:shop_chill_app/screens/register/repository/authenticate_repository.dart';
import 'package:shop_chill_app/screens/register/services/social_auth_utils.dart';
import 'package:shop_chill_app/screens/register/social_register_phone_confirm_page.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';
import 'package:shop_chill_app/shered/util/random_widget.dart';

class SocialAuthService {
  static final SocialAuthService _instance = SocialAuthService._internal();
  factory SocialAuthService() => _instance;
  SocialAuthService._internal();

  final ShopChillDatabase localDatabase = ShopChillDatabase();
  final BaseAuthenticateRepository _authenticateRepository = AuthenticateRepository();
  final SocialAuthUtils socialAuthUtils = SocialAuthUtils();

  Future<void> authenticate(
    BuildContext context, {
    User? user,
    required LoginFromWhere provider,
    LineUserProfile? lineUserProfile,
    bool isReactivationAccount = false,
  }) async {
    try {
      showLoading();
      final fcmToken = await getFcmToken();
      final db = await localDatabase.openDB();
      ProviderAuthResponse? loginResponse;

      switch (provider) {
        case LoginFromWhere.facebook:
          loginResponse = await socialAuthUtils.checkOrReActivateAndLogin<FacebookProfile>(
            context: context,
            uid: user?.uid,
            getLocalProfiles: () => db.facebookProfiles.where().findAll(),
            callLogin: () => _loginFacebook(user, fcmToken),
            deleteProfileByUid: (uid) async {
              await db.writeTxn(() async {
                await db.facebookProfiles.filter().uidEqualTo(uid).deleteFirst();
              });
            },
          );
          break;

        case LoginFromWhere.google:
          loginResponse = await socialAuthUtils.checkOrReActivateAndLogin<GoogleProfile>(
            context: context,
            uid: user?.uid,
            getLocalProfiles: () => db.googleProfiles.where().findAll(),
            callLogin: () => _loginGoogle(user, fcmToken),
            deleteProfileByUid: (uid) async {
              await db.writeTxn(() async {
                await db.googleProfiles.filter().uidEqualTo(uid).deleteFirst();
              });
            },
          );
          break;

        case LoginFromWhere.apple:
          loginResponse = await socialAuthUtils.checkOrReActivateAndLogin<AppleProfile>(
            context: context,
            uid: user?.uid,
            getLocalProfiles: () => db.appleProfiles.where().findAll(),
            callLogin: () => _loginApple(user, fcmToken),
            deleteProfileByUid: (uid) async {
              await db.writeTxn(() async {
                await db.appleProfiles.filter().uidEqualTo(uid).deleteFirst();
              });
            },
          );
          break;

        case LoginFromWhere.line:
          loginResponse = await socialAuthUtils.checkOrReActivateAndLogin<LineProfile>(
            context: context,
            uid: lineUserProfile?.profile?.userId,
            getLocalProfiles: () => db.lineProfiles.where().findAll(),
            callLogin: () => _loginLine(lineUserProfile!),
            deleteProfileByUid: (uid) async {
              await db.writeTxn(() async {
                await db.lineProfiles.filter().uidEqualTo(uid).deleteFirst();
              });
            },
          );
          break;

        case LoginFromWhere.others:
          break;
      }

      hideLoading();

      _handleLoginResponse(context, provider, loginResponse);
    } catch (e, st) {
      hideLoading();
      debugPrintStack(label: 'Social Auth Error: $e', stackTrace: st);
      showError("เกิดข้อผิดพลาด");
    } finally {
      hideLoading();
    }
  }

  // ----------------------- PROVIDER SPECIFIC -----------------------
  Future<ProviderAuthResponse?> _loginWithProvider(String? fcmToken, LoginFromWhere provider, {required ProviderAuthRequest request}) async {
    return _authenticateRepository.register(request: request, provider: provider);
  }

  Future<ProviderAuthResponse?> _loginFacebook(User? user, String? fcmToken) async {
    return _loginWithProvider(
      fcmToken,
      LoginFromWhere.facebook,
      request: ProviderAuthRequest(
        id: FFAppState().profileId,
        name: user?.displayName ?? '',
        email: user?.email ?? '',
        token: FFAppState().oauthToken,
        avatar: user?.photoURL ?? '',
        fcmToken: fcmToken ?? '',
      ),
    );
  }

  /*   Future<ApiCallResponse?> _loginFacebook(User? user, String? fcmToken) {
    return LoginWithFacebookCall.call(
      id: FFAppState().profileId,
      name: user?.displayName,
      email: user?.email,
      token: FFAppState().oauthToken,
      avatar: user?.photoURL,
      fcmToken: fcmToken,
    );
  } */

  Future<ProviderAuthResponse?> _loginGoogle(User? user, String? fcmToken) async {
    return _loginWithProvider(
      fcmToken,
      LoginFromWhere.google,
      request: ProviderAuthRequest(
        id: FFAppState().profileId,
        name: user?.displayName ?? '',
        email: user?.email ?? '',
        token: FFAppState().oauthToken,
        avatar: user?.photoURL ?? '',
        fcmToken: fcmToken ?? '',
      ),
    );
  }

  /*  Future<ApiCallResponse?> _loginGoogle(User? user, String? fcmToken) {
    return LoginWithGoogleCall.call(
      id: FFAppState().profileId,
      name: user?.displayName,
      email: user?.email,
      token: FFAppState().oauthToken,
      avatarOriginal: user?.photoURL,
      fcmToken: fcmToken,
    );
  } */

  Future<ProviderAuthResponse?> _loginApple(User? user, String? fcmToken) async {
    final String? email = user?.email ?? user?.providerData.firstOrNull?.email;
    return _loginWithProvider(
      fcmToken,
      LoginFromWhere.apple,
      request: ProviderAuthRequest(
        id: FFAppState().profileId,
        name: user?.displayName ?? '', //generateRandomUser(),
        email: email ?? '',
        token: FFAppState().oauthToken,
        fcmToken: fcmToken ?? '',
      ),
    );
  }

  /* Future<ApiCallResponse?> _loginApple(User? user, String? fcmToken) {
    final String? email = user?.email ?? user?.providerData.firstOrNull?.email;

    return LoginWithAppleCall.call(
      id: FFAppState().profileId,
      name: generateRandomUser(),
      username: generateRandomUsername(),
      email: email,
      token: FFAppState().oauthToken,
      fcmToken: fcmToken,
    );
  }
 */

  Future<ProviderAuthResponse?> _loginLine(LineUserProfile lineProfile) async {
    return _loginWithProvider(
      null,
      LoginFromWhere.line,
      request: ProviderAuthRequest(
        id: lineProfile.profile?.userId ?? '',
        name: lineProfile.profile?.displayName ?? '',
        email: lineProfile.email ?? '',
        token: lineProfile.token ?? '',
        avatar: lineProfile.profile?.pictureUrl,
        fcmToken: '',
      ),
    );
  }
  /*   Future<ApiCallResponse?> _loginLine(LineUserProfile lineProfile) {
    return LoginWithLineCall.call(
      id: lineProfile.profile?.userId,
      name: lineProfile.profile?.displayName,
      email: lineProfile.email,
      avatar: lineProfile.profile?.pictureUrl,
      token: lineProfile.token,
    );
  } */

  void _handleLoginResponse(BuildContext context, LoginFromWhere provider, ProviderAuthResponse? response) {
    if (response == null) {
      showError("เกิดข้อผิดพลาดบางอย่างกรุณาลองใหม่");
      return;
    }

    final token = (response.data?.apiToken ?? '').toString();

    if (response.status == true) {
      if (response.code == '9999' || response.data?.phone == null) {
        Navigator.push(context, CupertinoPageRoute(builder: (context) => SocialRegisterPhoneConfirmPage(providerAuthResponse: response)));
      } else if (token.isNotEmpty) {
        FFAppState().token = token;
        context.read<UserBloc>().add(const GetUserProfile());
        context.read<CartBloc>().add(GetCartEvent());
        context.read<OrderCountCubit>().fetchOrderCount();
        context.read<HomeBloc>().add(GetHomeEvent(context));
        context.read<AllChatRoomCubit>().oninitial();

        Navigator.of(context).popUntil((route) => route.isFirst);
      } else {
        showError(response.message ?? 'เกิดข้อผิดพลาด');
      }
    } else {
      showError(response.message ?? 'เกิดข้อผิดพลาด');
    }
  }
}

/* 
import 'package:shop_chill_app/shered/util/random_widget.dart';

class SocialAuthService {
  static final SocialAuthService _instance = SocialAuthService._internal();
  factory SocialAuthService() => _instance;
  SocialAuthService._internal();

  final ShopChillDatabase localDatabase = ShopChillDatabase();

  Future<void> authenticate(BuildContext context, {User? user, required LoginFromWhere provider, LineUserProfile? lineUserProfile, bool isReactivationAccount = false}) async {
    try {
      showLoading();
      final fcmToken = await getFcmToken();
      final db = await localDatabase.openDB();
      ApiCallResponse? loginResponse;

      switch (provider) {
        case LoginFromWhere.facebook:
          loginResponse = await _checkOrReActivateAndLogin<FacebookProfile>(
            context: context,
            uid: user?.uid,
            getLocalProfiles: () => db.facebookProfiles.where().findAll(),
            callLogin: () => _loginFacebook(user, fcmToken),
            deleteProfileByUid: (uid) async {
              await db.writeTxn(() async {
                await db.facebookProfiles.filter().uidEqualTo(uid).deleteFirst();
              });
            },
          );
          break;

        case LoginFromWhere.google:
          loginResponse = await _checkOrReActivateAndLogin<GoogleProfile>(
            context: context,
            uid: user?.uid,
            getLocalProfiles: () => db.googleProfiles.where().findAll(),
            callLogin: () => _loginGoogle(user, fcmToken),
            deleteProfileByUid: (uid) async {
              await db.writeTxn(() async {
                await db.googleProfiles.filter().uidEqualTo(uid).deleteFirst();
              });
            },
          );
          break;

        case LoginFromWhere.apple:
          loginResponse = await _checkOrReActivateAndLogin<AppleProfile>(
            context: context,
            uid: user?.uid,
            getLocalProfiles: () => db.appleProfiles.where().findAll(),
            callLogin: () => _loginApple(user, fcmToken),
            deleteProfileByUid: (uid) async {
              await db.writeTxn(() async {
                await db.appleProfiles.filter().uidEqualTo(uid).deleteFirst();
              });
            },
          );
          break;

        case LoginFromWhere.line:
          loginResponse = await _checkOrReActivateAndLogin<LineProfile>(
            context: context,
            uid: lineUserProfile?.profile?.userId,
            getLocalProfiles: () => db.lineProfiles.where().findAll(),
            callLogin: () => _loginLine(lineUserProfile!),
            deleteProfileByUid: (uid) async {
              await db.writeTxn(() async {
                await db.lineProfiles.filter().uidEqualTo(uid).deleteFirst();
              });
            },
          );
          break;

        case LoginFromWhere.others:
          break;
      }

      _handleLoginResponse(context, provider, loginResponse);
    } catch (e, st) {
      hideLoading();
      debugPrintStack(label: 'Social Auth Error: $e', stackTrace: st);
      showError("เกิดข้อผิดพลาด");
    } finally {
      hideLoading();
    }
  }

  Future<ApiCallResponse?> _checkOrReActivateAndLogin<T>({
    required BuildContext context,
    required String? uid,
    required Future<List<T>> Function() getLocalProfiles,
    required Future<ApiCallResponse?> Function() callLogin,
    required Future<void> Function(String? uid) deleteProfileByUid,
  }) async {
    final isExist = (await getLocalProfiles()).any((e) {
      if (e is FacebookProfile) return e.uid == uid;
      if (e is GoogleProfile) return e.uid == uid;
      if (e is AppleProfile) return e.uid == uid;
      if (e is LineProfile) return e.uid == uid;
      return false;
    });

    if (isExist) {
      final completer = Completer<ApiCallResponse?>();
      ShopChillDialogs().showReactivationAccountDialog(
        context,
        callback: () async {
          await deleteProfileByUid(uid);
          final response = await callLogin();
          completer.complete(response);
        },
      );
      return completer.future;
    } else {
      return callLogin();
    }
  }

  // ----------------------- PROVIDER SPECIFIC -----------------------

  Future<ApiCallResponse?> _loginFacebook(User? user, String? fcmToken) {
    return LoginWithFacebookCall.call(
      id: FFAppState().profileId,
      name: user?.displayName,
      email: user?.email,
      token: FFAppState().oauthToken,
      avatar: user?.photoURL,
      fcmToken: fcmToken,
    );
  }

  Future<ApiCallResponse?> _loginGoogle(User? user, String? fcmToken) {
    return LoginWithGoogleCall.call(
      id: FFAppState().profileId,
      name: user?.displayName,
      email: user?.email,
      token: FFAppState().oauthToken,
      avatarOriginal: user?.photoURL,
      fcmToken: fcmToken,
    );
  }

  Future<ApiCallResponse?> _loginApple(User? user, String? fcmToken) {
    final String? email = user?.email ?? user?.providerData.firstOrNull?.email;

    return LoginWithAppleCall.call(
      id: FFAppState().profileId,
      name: generateRandomUser(),
      username: generateRandomUsername(),
      email: email,
      token: FFAppState().oauthToken,
      fcmToken: fcmToken,
    );
  }

  Future<ApiCallResponse?> _loginLine(LineUserProfile lineProfile) {
    return LoginWithLineCall.call(
      id: lineProfile.profile?.userId,
      name: lineProfile.profile?.displayName,
      email: lineProfile.email,
      avatar: lineProfile.profile?.pictureUrl,
      token: lineProfile.token,
    );
  }

  void _handleLoginResponse(BuildContext context, LoginFromWhere provider, ApiCallResponse? loginResponse) {
    if (loginResponse == null) {
      hideLoading();
      showError("Login failed.");
      return;
    }

    final bool status = _getStatusFromResponse(provider, loginResponse);
    final String? token = _getTokenFromResponse(provider, loginResponse);

    if (status == true && token != null && token.isNotEmpty) {
      hideLoading();
      FFAppState().token = token;
      context.read<UserBloc>().add(const GetUserProfile());
      context.read<CartBloc>().add(GetCartEvent());
      context.read<OrderCountCubit>().fetchOrderCount();
      context.read<HomeBloc>().add(GetHomeEvent(context));
      context.read<AllChatRoomCubit>().oninitial();

      Navigator.of(context).popUntil((route) => route.isFirst);
    } else {
      hideLoading();
      _handleError(context, provider, loginResponse);
    /*   if (provider == LoginFromWhere.apple) {
        _showRegisterApple(context, loginResponse);
      } */
    }
  }

  bool _getStatusFromResponse(LoginFromWhere provider, ApiCallResponse response) {
    switch (provider) {
      case LoginFromWhere.facebook:
        return LoginWithFacebookCall.status(response.jsonBody ?? '');
      case LoginFromWhere.google:
        return LoginWithGoogleCall.status(response.jsonBody ?? '');
      case LoginFromWhere.apple:
        return LoginWithAppleCall.status(response.jsonBody ?? '');
      case LoginFromWhere.line:
        return LoginWithLineCall.status(response.jsonBody ?? '');
      case LoginFromWhere.others:
        return false;
    }
  }

  String? _getTokenFromResponse(LoginFromWhere provider, ApiCallResponse response) {
    switch (provider) {
      case LoginFromWhere.facebook:
        return LoginWithFacebookCall.token(response.jsonBody ?? '').toString();
      case LoginFromWhere.google:
        return LoginWithGoogleCall.token(response.jsonBody ?? '').toString();
      case LoginFromWhere.apple:
        return LoginWithAppleCall.token(response.jsonBody ?? '')?.toString();
      case LoginFromWhere.line:
        return LoginWithLineCall.token(response.jsonBody ?? '')?.toString();
      case LoginFromWhere.others:
        return null;
    }
  }

  void _handleError(BuildContext context, LoginFromWhere provider, ApiCallResponse response) {
    switch (provider) {
      case LoginFromWhere.facebook:
        showError(LoginWithFacebookCall.message(response.jsonBody ?? ''));
        break;
      case LoginFromWhere.google:
        showError(LoginWithGoogleCall.message(response.jsonBody ?? ''));
        break;
      case LoginFromWhere.apple:
        showError(LoginWithAppleCall.message(response.jsonBody ?? ''));
        break;
      case LoginFromWhere.line:
        showError(LoginWithLineCall.message(response.jsonBody ?? ''));
        break;
      case LoginFromWhere.others:
        break;
    }
  }

/*   Future<void> _showRegisterApple(BuildContext context, ApiCallResponse response) async {
    await Navigator.push(
      context,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        duration: const Duration(milliseconds: 0),
        reverseDuration: const Duration(milliseconds: 0),
        child: RegisterAppleWidget(email: response.jsonBody['email']),
      ),
    );
  } */
} */

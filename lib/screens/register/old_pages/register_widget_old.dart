/* import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';

import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' show LucideIcons;
import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/auth/facebook_auth.dart';
import 'package:shop_chill_app/auth/line_auth.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/config/theme_config.dart';
import 'package:shop_chill_app/screens/login/utils/sms_retriever_impl.dart';
import 'package:shop_chill_app/screens/login/widgets/reusable_social_login_buttons.dart';
import 'package:shop_chill_app/screens/login/widgets/social_login_buttons.dart';
import 'package:shop_chill_app/screens/register/check_data_login.dart';
import 'package:shop_chill_app/screens/register/check_verify_model.dart';
import 'package:shop_chill_app/screens/register/register_full_widget.dart';
import 'package:shop_chill_app/screens/register/services/social_auth_service.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';
import 'package:shop_chill_app/shered/util/custom_text.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';

import '../../auth/auth_util.dart';
import '../../backend/api_requests/api_calls.dart';
import '../../config/shopchill_loading/shopchill_loading.dart';
import '../flutter_flow/flutter_flow_icon_button.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import '../flutter_flow/flutter_flow_widgets.dart';
import '../login/login_widget.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class RegisterWidget extends StatefulWidget {
  const RegisterWidget({super.key});

  @override
  _RegisterWidgetState createState() => _RegisterWidgetState();
}

class _RegisterWidgetState extends State<RegisterWidget> {
  late ApiCallResponse registerReponse;
  TextEditingController confirmPasswordController = TextEditingController();
  late bool confirmPasswordVisibility;
  TextEditingController emailAddressController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController userNameController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController pinController = TextEditingController();
  FocusNode pinNode = FocusNode();
  final formKey = GlobalKey<FormState>();
  late bool passwordVisibility;
  bool? checkboxListTileValue = false;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  int countdown = 60;
  bool isCountdown = false;
  bool isAnimated = false;
  String refcode = '';

  final authService = SocialAuthService();

  @override
  void initState() {
    super.initState();
    confirmPasswordVisibility = false;
    passwordVisibility = false;
  }

  void onTimerStart() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown > 0) {
        setState(() {
          isCountdown = true;
          countdown--;
        });
      } else {
        //countdown = 60;
        setState(() {
          isCountdown = false;
          print('on countdown stop');
        });

        timer.cancel();
      }
    });
  }

  /* Future<void> getLoginWithFacebook(User user) async {
    ApiCallResponse? loginResponse = await LoginWithFacebookCall.call(
      name: user.displayName,
      email: user.email,
      id: FFAppState().profileId,
      token: FFAppState().oauthToken,
      avatarOriginal: user.photoURL,
    );
    var shouldSetState = true;
    if (LoginWithFacebookCall.status(
          (loginResponse.jsonBody ?? ''),
        ) &&
        LoginWithFacebookCall.token(
          (loginResponse.jsonBody ?? ''),
        ).toString().isNotEmpty) {
      hideLoading();
      FFAppState().token = LoginWithFacebookCall.token(
        (loginResponse.jsonBody ?? ''),
      ).toString();
      context.read<UserBloc>().add(GetUserProfile());
      Navigator.pop(context, true);
      Navigator.pop(context, true);
    } else {
      hideLoading();
      showError(LoginWithFacebookCall.message((loginResponse.jsonBody ?? '')));
      if (shouldSetState) setState(() {});
      return;
    }
    hideLoading();

    if (shouldSetState) setState(() {});
    return;
  }

  Future<void> getLoginWithGoogle(User user) async {
    ApiCallResponse? loginResponse = await LoginWithGoogleCall.call(
      name: user.displayName,
      email: user.email,
      id: FFAppState().profileId,
      token: FFAppState().oauthToken,
      avatarOriginal: user.photoURL,
    );
    var shouldSetState = true;
    if (LoginWithGoogleCall.status(
          (loginResponse.jsonBody ?? ''),
        ) &&
        LoginWithGoogleCall.token(
          (loginResponse.jsonBody ?? ''),
        ).toString().isNotEmpty) {
      hideLoading();
      FFAppState().token = LoginWithGoogleCall.token(
        (loginResponse.jsonBody ?? ''),
      ).toString();
      context.read<UserBloc>().add(GetUserProfile());
      Navigator.pop(context, true);
      Navigator.pop(context, true);
    } else {
      hideLoading();
      showError(LoginWithGoogleCall.message((loginResponse.jsonBody ?? '')));
      if (shouldSetState) setState(() {});
      return;
    }
    hideLoading();

    if (shouldSetState) setState(() {});
    return;
  }

  Future<void> getLoginWithApple(User user) async {
    final String? fcmToken = await getFcmToken();
    ApiCallResponse? loginResponse = await LoginWithAppleCall.call(
      name: generateRandomUser(),
      username: generateRandomUsername(),
      // phone: '0000000000',
      email: user.email,
      token: FFAppState().oauthToken,
      id: FFAppState().profileId,
      fcmToken: fcmToken,
    );
    var shouldSetState = true;
    if (LoginWithAppleCall.status(
          (loginResponse.jsonBody ?? ''),
        ) &&
        LoginWithAppleCall.token(
              (loginResponse.jsonBody ?? ''),
            ) !=
            null) {
      hideLoading();
      FFAppState().token = LoginWithAppleCall.token(
        (loginResponse.jsonBody ?? ''),
      ).toString();
      context.read<UserBloc>().add(GetUserProfile());
      Navigator.pop(context, true);
      Navigator.pop(context, true);
    } else {
      hideLoading();

      await Navigator.push(
        context,
        PageTransition(
          type: PageTransitionType.rightToLeft,
          duration: Duration(milliseconds: 0),
          reverseDuration: Duration(milliseconds: 0),
          child: RegisterAppleWidget(
            email: user.email,
          ),
        ),
      );
      if (shouldSetState) setState(() {});
      return;
    }
    hideLoading();

    if (shouldSetState) setState(() {});
    return;
  } */

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }
        _onPushToLoginButtonPressed();
      },

      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        body: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  _buildShopChillLogo(),
                  _buildHeader(context),
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPhoneNumberTextFormFirldSection(context),
                        const SizedBox(height: 20),
                        _buildOTPCodeSection(context),
                        _buildRegisterButtonSection(context),
                        _buildSocialAuthSection(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShopChillLogo() {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 60, 0, 16),
      child: Center(child: Image.asset(UImageAssets.HORIZONTAL_LOGO, width: 186, height: 64, fit: BoxFit.fitWidth)),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Align(
      alignment: const AlignmentDirectional(0, 0),
      child: Stack(
        alignment: const AlignmentDirectional(0, 0),
        children: [
          Align(
            alignment: const AlignmentDirectional(-1, 0),
            child: FlutterFlowIconButton(
              borderColor: Colors.transparent,
              borderRadius: 30,
              borderWidth: 1,
              buttonSize: 60,
              icon: const FaIcon(FontAwesomeIcons.angleLeft, color: Colors.black, size: 20),
              onPressed: () async {
                _onPushToLoginButtonPressed();
              },
            ),
          ),
          Align(
            alignment: const AlignmentDirectional(0, 0),
            child: Text(
              'สมัครสมาชิกใหม่',
              textAlign: TextAlign.center,
              style: FlutterFlowTheme.of(context).title1.override(fontFamily: 'Sarabun', color: Colors.black, fontSize: 26, fontWeight: FontWeight.w700, useGoogleFonts: false),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneNumberTextFormFirldSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('หมายเลขโทรศัพท์', style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black)),
                8.gap,
                TextFormField(
                  maxLength: 10,
                  controller: phoneController,
                  obscureText: false,
                  onChanged: (value) {
                    setState(() {});
                  },
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'กรุณากรอกเบอร์โทร';
                    } else {
                      return null;
                    }
                  },
                  decoration: InputDecoration(
                    suffix: GestureDetector(
                      onTap: isCountdown == true
                          ? () {
                              print('on 0');
                            }
                          : () async {
                              if (formKey.currentState!.validate()) {
                                showLoading();
                                setState(() {
                                  countdown = 60;
                                });

                                await RegisterCall().registerOtp(phone: phoneController.text).then((value) {
                                  if (value['status'] == true) {
                                    hideLoading();
                                    setState(() {
                                      pinNode.requestFocus();
                                      onTimerStart();
                                      isAnimated = true;
                                      print('rref = ${value['ref_code']}');
                                      refcode = value['ref_code'];
                                    });

                                    ShopChillLoading.showToast('ส่ง otp สำเสร็จ');
                                  } else {
                                    hideLoading();
                                    ShopChillLoading.showToast('มีปัญหาบางอย่าง');
                                  }
                                });
                              }
                            },
                      child: isCountdown == false
                          ? Text('ส่ง OTP', style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 14, color: ColorThemeConfig.primaryColor))
                          : Text(countdown.toString(), style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 14, color: ColorThemeConfig.primaryColor)),
                    ),
                    counterText: '',
                    icon: const Icon(LucideIcons.phone, size: 20, color: ColorThemeConfig.unfocusedTextColor),

                    labelStyle: FlutterFlowTheme.of(context).bodyText2,
                    labelText: 'หมายเลขโทรศัพท์',
                    hintStyle: FlutterFlowTheme.of(context).bodyText2,
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: FlutterFlowTheme.of(context).primaryBackground, width: 1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    floatingLabelBehavior: FloatingLabelBehavior.never,
                    filled: true,
                    fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                    contentPadding: const EdgeInsets.all(10),
                  ),
                  style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black),
                  keyboardType: TextInputType.phone,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOTPCodeSection(BuildContext context) {
    return AnimatedContainer(
      height: isAnimated ? 200 : 0,
      duration: const Duration(milliseconds: 200),
      alignment: Alignment.topCenter,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'รหัสอ้างอิง ($refcode)',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: ColorThemeConfig.unfocusedTextColor, fontWeight: FontWeight.w500),
            ),
            Text('Shop Chill จะส่งรหัสยืนยันตัวตนให้คุณ\nผ่านทาง SMS ไปยัง ', textAlign: TextAlign.center, style: Theme.of(context).textTheme.bodyMedium),
            20.gap,
            _pinputSection(),
          ],
        ),
      ),
    );
  }

  Widget _pinputSection() {
    const length = 6;

    const errorColor = Color.fromRGBO(255, 234, 238, 1);
    const fillColor = Color.fromRGBO(222, 231, 240, .57);
    final defaultPinTheme = PinTheme(
      width: 50,
      height: 60,
      textStyle: GoogleFonts.poppins(fontSize: 22, color: const Color.fromRGBO(30, 60, 87, 1)),
      decoration: BoxDecoration(
        color: fillColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.transparent),
      ),
    );

    return SizedBox(
      height: 60,
      child: Pinput(
        onChanged: (value) {
          setState(() {});
        },
        length: length,
        controller: pinController,
        smsRetriever: SmsRetrieverImpl(),
        focusNode: pinNode,
        defaultPinTheme: defaultPinTheme,
        onCompleted: (pin) {
          // setState(() => otp = pin);
        },
        focusedPinTheme: defaultPinTheme.copyWith(
          height: 60,
          width: 60,
          decoration: defaultPinTheme.decoration!.copyWith(border: Border.all(color: ColorThemeConfig.primaryColor)),
        ),
        errorPinTheme: defaultPinTheme.copyWith(
          decoration: BoxDecoration(color: errorColor, borderRadius: BorderRadius.circular(10)),
        ),
      ),
    );
  }

  Widget _buildRegisterButtonSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: pinController.text.isEmpty || phoneController.text.isEmpty
            ? () {}
            : () async {
                ShopChillLoading.show();
                await LoginWithOtpCall.check(mobile: phoneController.text, otp: pinController.text).then((value) {
                  print('val = $value');
                  if (value['status'] == true) {
                    final CheckVerifyModel check = CheckVerifyModel.fromJson(value);
                    ShopChillLoading.dismiss();
                    Navigator.pushReplacement(
                      context,
                      CupertinoPageRoute(
                        builder: (context) {
                          return CheckDataLogin(checkVerifyModel: check);
                        },
                      ),
                    );
                  } else if (value['status'] == false && value['msg'] == 'Error check otp') {
                    print('otp fail');
                    ShopChillLoading.showError(value['msg']);
                  } else if (value['status'] == false && value['msg'] == 'คุณยังไม่เป้นสมาชิก กรุณาสมัครสมาชิกก่อน') {
                    ShopChillLoading.dismiss();
                    Navigator.pushReplacement(
                      context,
                      CupertinoPageRoute(
                        builder: (context) {
                          return RegisterFullWidget(phone: phoneController.text);
                        },
                      ),
                    );
                    print('register');
                  } else if (value['status'] == false && value['msg'] == 'OTP Data Not Found') {
                    print('otp fail');
                    ShopChillLoading.showError(value['msg']);
                  } else {
                    ShopChillLoading.showError(value['message'] ?? 'error');
                    print('error');
                  }
                });
              },
        child: Container(
          width: double.infinity,
          height: 50,
          padding: const EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
            color: pinController.text.isEmpty || phoneController.text.isEmpty ? Colors.grey : ColorThemeConfig.primaryColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              'สมัครสมาชิก',
              textAlign: TextAlign.center,
              style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.white),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSocialAuthSection(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        20.gap,
        Stack(
          alignment: Alignment.center,
          children: [
            const Expanded(child: Divider()),
            Container(
              margin: const EdgeInsets.only(bottom: 5),
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: AutoSizeText(
                'หรือสมัครสมาชิกด้วย',
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).subtitle1.copyWith(color: ColorThemeConfig.unfocusedTextColor),
                maxFontSize: 14,
              ),
            ),
          ],
        ),
        30.gap,
        const ReusableSocialLoginButtons(),
        _buildCanLoginSection(context),
      ],
    );
  }

  Widget _buildCanLoginSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 50, 0, 30),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('หากคุณมีบัญชีผู้ใช้แล้วคุณสามารถ', style: Theme.of(context).textTheme.labelMedium),

          5.gap,
          GestureDetector(
            onTap: () async {
              _onPushToLoginButtonPressed();
            },
            child: Text(
              'เข้าสู่ระบบ',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(color: ColorThemeConfig.primaryColor, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _onPushToLoginButtonPressed() {
    Navigator.pushNamedAndRemoveUntil(context, AppRoutes.login, (route) => route.settings.name == AppRoutes.home || route.settings.name == AppRoutes.navBar);
  }
}



/* import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_ShopChillLoading/flutter_ShopChillLoading.dart';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';
import 'package:shop_chill_app/screens/register/check_data_login.dart';
import 'package:shop_chill_app/screens/register/check_verify_model.dart';
import 'package:shop_chill_app/screens/register/register_apple_widget.dart';
import 'package:shop_chill_app/screens/register/register_full_widget.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';

import '../../app_state.dart';
import '../../auth/auth_util.dart';
import '../../backend/api_requests/api_calls.dart';
import '../flutter_flow/flutter_flow_icon_button.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import '../flutter_flow/flutter_flow_widgets.dart';
import '../login/login_widget.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../myaccount/bloc/user/user_bloc.dart';
import '../notification/fcm/fcm_bloc.dart';
import '../../shered/util/random_widget.dart';

class RegisterWidget extends StatefulWidget {
  const RegisterWidget({Key? key}) : super(key: key);

  @override
  _RegisterWidgetState createState() => _RegisterWidgetState();
}

class _RegisterWidgetState extends State<RegisterWidget> {
  late ApiCallResponse registerReponse;
  TextEditingController confirmPasswordController = TextEditingController();
  late bool confirmPasswordVisibility;
  TextEditingController emailAddressController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController userNameController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController pinController = TextEditingController();
  FocusNode pinNode = FocusNode();
  final formKey = GlobalKey<FormState>();
  late bool passwordVisibility;
  bool? checkboxListTileValue = false;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  int countdown = 60;
  bool isCountdown = false;
  bool isAnimated = false;
  String refcode = '';

  @override
  void initState() {
    super.initState();
    //confirmPasswordController = TextEditingController();
    confirmPasswordVisibility = false;
    // emailAddressController = TextEditingController();
    // phoneController = TextEditingController();
    // nameController = TextEditingController();
    // userNameController = TextEditingController();
    // passwordController = TextEditingController();
    passwordVisibility = false;
  }

  void onTimerStart() {
    Timer.periodic(Duration(seconds: 1), (timer) {
      if (countdown > 0) {
        setState(() {
          isCountdown = true;
          countdown--;
        });
      } else {
        //countdown = 60;
        setState(() {
          isCountdown = false;
          print('on countdown stop');
        });

        timer.cancel();
      }
    });
  }

  Future<void> getLoginWithFacebook(User user) async {
    ApiCallResponse? loginResponse = await LoginWithFacebookCall.call(
      name: user.displayName,
      email: user.email,
      id: FFAppState().profileId,
      token: FFAppState().oauthToken,
      avatarOriginal: user.photoURL,
    );
    var shouldSetState = true;
    if (LoginWithFacebookCall.status(
          (loginResponse.jsonBody ?? ''),
        ) &&
        LoginWithFacebookCall.token(
          (loginResponse.jsonBody ?? ''),
        ).toString().isNotEmpty) {
      hideLoading();
      FFAppState().token = LoginWithFacebookCall.token(
        (loginResponse.jsonBody ?? ''),
      ).toString();
      context.read<UserBloc>().add(GetUserProfile());
      Navigator.pop(context, true);
      Navigator.pop(context, true);
    } else {
      hideLoading();
      showError(LoginWithFacebookCall.message((loginResponse.jsonBody ?? '')));
      if (shouldSetState) setState(() {});
      return;
    }
    hideLoading();

    if (shouldSetState) setState(() {});
    return;
  }

  Future<void> getLoginWithGoogle(User user) async {
    ApiCallResponse? loginResponse = await LoginWithGoogleCall.call(
      name: user.displayName,
      email: user.email,
      id: FFAppState().profileId,
      token: FFAppState().oauthToken,
      avatarOriginal: user.photoURL,
    );
    var shouldSetState = true;
    if (LoginWithGoogleCall.status(
          (loginResponse.jsonBody ?? ''),
        ) &&
        LoginWithGoogleCall.token(
          (loginResponse.jsonBody ?? ''),
        ).toString().isNotEmpty) {
      hideLoading();
      FFAppState().token = LoginWithGoogleCall.token(
        (loginResponse.jsonBody ?? ''),
      ).toString();
      context.read<UserBloc>().add(GetUserProfile());
      Navigator.pop(context, true);
      Navigator.pop(context, true);
    } else {
      hideLoading();
      showError(LoginWithGoogleCall.message((loginResponse.jsonBody ?? '')));
      if (shouldSetState) setState(() {});
      return;
    }
    hideLoading();

    if (shouldSetState) setState(() {});
    return;
  }

  Widget animatedBorders() {
    const length = 6;
    const borderColor = Color.fromRGBO(114, 178, 238, 1);
    const errorColor = Color.fromRGBO(255, 234, 238, 1);
    const fillColor = Color.fromRGBO(222, 231, 240, .57);
    final defaultPinTheme = PinTheme(
      width: 60,
      height: 60,
      textStyle: GoogleFonts.poppins(
        fontSize: 22,
        color: Color.fromRGBO(30, 60, 87, 1),
      ),
      decoration: BoxDecoration(
        color: fillColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.transparent),
      ),
    );

    return SizedBox(
      height: 60,
      child: Pinput(
        onChanged: (value) {
          setState(() {});
        },
        length: length,
        controller: pinController,
        focusNode: pinNode,
        defaultPinTheme: defaultPinTheme,
        onCompleted: (pin) {
          // setState(() => otp = pin);
        },
        focusedPinTheme: defaultPinTheme.copyWith(
          height: 60,
          width: 60,
          decoration: defaultPinTheme.decoration!.copyWith(
            border: Border.all(color: borderColor),
          ),
        ),
        errorPinTheme: defaultPinTheme.copyWith(
          decoration: BoxDecoration(
            color: errorColor,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Future<void> getLoginWithApple(User user) async {
    final String? fcmToken = await getFcmToken();
    ApiCallResponse? loginResponse = await LoginWithAppleCall.call(
      name: generateRandomUser(),
      username: generateRandomUsername(),
      // phone: '0000000000',
      email: user.email,
      token: FFAppState().oauthToken,
      id: FFAppState().profileId,
      fcmToken: fcmToken,
    );
    var shouldSetState = true;
    if (LoginWithAppleCall.status(
          (loginResponse.jsonBody ?? ''),
        ) &&
        LoginWithAppleCall.token(
              (loginResponse.jsonBody ?? ''),
            ) !=
            null) {
      hideLoading();
      FFAppState().token = LoginWithAppleCall.token(
        (loginResponse.jsonBody ?? ''),
      ).toString();
      context.read<UserBloc>().add(GetUserProfile());
      Navigator.pop(context, true);
      Navigator.pop(context, true);
    } else {
      hideLoading();

      await Navigator.push(
        context,
        PageTransition(
          type: PageTransitionType.rightToLeft,
          duration: Duration(milliseconds: 0),
          reverseDuration: Duration(milliseconds: 0),
          child: RegisterAppleWidget(
            email: user.email,
          ),
        ),
      );
      if (shouldSetState) setState(() {});
      return;
    }
    hideLoading();

    if (shouldSetState) setState(() {});
    return;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0, 50, 0, 16),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            // 'assets/images/logo_horizontal.png',
                             UImageAssets.HORIZONTAL_LOGO,
                            width: 186,
                            height: 64,
                            fit: BoxFit.fitWidth,
                          ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: AlignmentDirectional(0, 0),
                      child: Stack(
                        alignment: AlignmentDirectional(0, 0),
                        children: [
                          Align(
                            alignment: AlignmentDirectional(-1, 0),
                            child: FlutterFlowIconButton(
                              borderColor: Colors.transparent,
                              borderRadius: 30,
                              borderWidth: 1,
                              buttonSize: 60,
                              icon: FaIcon(
                                FontAwesomeIcons.angleLeft,
                                color: Colors.black,
                                size: 30,
                              ),
                              onPressed: () async {
                                Navigator.pop(context);
                              },
                            ),
                          ),
                          Align(
                            alignment: AlignmentDirectional(0, 0),
                            child: Text(
                              'สมัครสมาชิกใหม่',
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context).title1.override(
                                    fontFamily: 'Sarabun',
                                    color: Colors.black,
                                    fontSize: 26,
                                    fontWeight: FontWeight.w700,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Padding(
                          //   padding: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                          //   child: Row(
                          //     mainAxisSize: MainAxisSize.max,
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     children: [
                          //       Expanded(
                          //         child: Column(
                          //           mainAxisSize: MainAxisSize.max,
                          //           crossAxisAlignment: CrossAxisAlignment.start,
                          //           children: [
                          //             Text(
                          //               'กรอกชื่อผู้ใช้งาน',
                          //               style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                     fontFamily: 'Sarabun',
                          //                     color: Colors.black,
                          //                     useGoogleFonts: false,
                          //                   ),
                          //             ),
                          //             Padding(
                          //               padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //               child: Container(
                          //                 width: double.infinity,
                          //                 decoration: BoxDecoration(),
                          //                 child: TextFormField(
                          //                   controller: userNameController,
                          //                   obscureText: false,
                          //                   decoration: InputDecoration(
                          //                     labelStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     labelText: 'John',
                          //                     floatingLabelBehavior: FloatingLabelBehavior.never,
                          //                     hintStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     enabledBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     focusedBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     filled: true,
                          //                     fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                          //                     contentPadding: EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                          //                   ),
                          //                   style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                         fontFamily: 'Sarabun',
                          //                         fontSize: 16,
                          //                         useGoogleFonts: false,
                          //                       ),
                          //                 ),
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                          // Padding(
                          //   padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //   child: Row(
                          //     mainAxisSize: MainAxisSize.max,
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     children: [
                          //       Expanded(
                          //         child: Column(
                          //           mainAxisSize: MainAxisSize.max,
                          //           crossAxisAlignment: CrossAxisAlignment.start,
                          //           children: [
                          //             Text(
                          //               'ชื่อ-นามสกุล',
                          //               style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                     fontFamily: 'Sarabun',
                          //                     color: Colors.black,
                          //                     useGoogleFonts: false,
                          //                   ),
                          //             ),
                          //             Padding(
                          //               padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //               child: Container(
                          //                 width: double.infinity,
                          //                 decoration: BoxDecoration(),
                          //                 child: TextFormField(
                          //                   controller: nameController,
                          //                   obscureText: false,
                          //                   decoration: InputDecoration(
                          //                     labelStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     labelText: 'ชื่อ นามสกุล',
                          //                     floatingLabelBehavior: FloatingLabelBehavior.never,
                          //                     hintStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     enabledBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     focusedBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     filled: true,
                          //                     fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                          //                     contentPadding: EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                          //                   ),
                          //                   style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                         fontFamily: 'Sarabun',
                          //                         fontSize: 16,
                          //                         useGoogleFonts: false,
                          //                       ),
                          //                 ),
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),

                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'หมายเลขโทรศัพท์',
                                        style: FlutterFlowTheme.of(context).bodyText1.override(
                                              fontFamily: 'Sarabun',
                                              color: Colors.black,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                                        child: Container(
                                          width: double.infinity,
                                          decoration: BoxDecoration(),
                                          child: TextFormField(
                                            onChanged: (value) {
                                              setState(() {});
                                            },
                                            validator: (value) {
                                              if (value!.isEmpty) {
                                                return 'กรุณากรอกเบอร์โทร';
                                              } else {
                                                return null;
                                              }
                                            },
                                            controller: phoneController,
                                            obscureText: false,
                                            maxLength: 10,
                                            decoration: InputDecoration(
                                              suffix: Padding(
                                                padding: const EdgeInsets.only(right: 12),
                                                child: GestureDetector(
                                                    onTap: isCountdown == true
                                                        ? () {
                                                            print('on 0');
                                                          }
                                                        : () async {
                                                            if (formKey.currentState!.validate()) {
                                                              showLoading();
                                                              setState(() {
                                                                countdown = 60;
                                                              });

                                                              await RegisterCall().registerOtp(phone: phoneController.text).then((value) {
                                                                if (value['status'] == true) {
                                                                  hideLoading();
                                                                  setState(() {
                                                                    pinNode.requestFocus();
                                                                    onTimerStart();
                                                                    isAnimated = true;
                                                                    print('rref = ${value['ref_code']}');
                                                                    refcode = value['ref_code'];
                                                                  });

                                                                  Fluttertoast.showToast(msg: 'ส่ง otp สำเสร็จ', gravity: ToastGravity.CENTER);
                                                                } else {
                                                                  hideLoading();
                                                                  Fluttertoast.showToast(msg: 'มีปัญหาบางอย่าง');
                                                                }
                                                              });
                                                            }
                                                          },
                                                    child: isCountdown == false
                                                        ? Text(
                                                            'ส่ง OTP',
                                                            style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false, color: Colors.blue),
                                                          )
                                                        : Text(
                                                            countdown.toString(),
                                                            style: FlutterFlowTheme.of(context).bodyText1.override(fontFamily: 'Sarabun', fontSize: 16, useGoogleFonts: false, color: Colors.blue),
                                                          )),
                                              ),
                                              counterText: '',
                                              labelStyle: FlutterFlowTheme.of(context).bodyText2,
                                              labelText: '081xxxxxxx',
                                              floatingLabelBehavior: FloatingLabelBehavior.never,
                                              hintStyle: FlutterFlowTheme.of(context).bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: FlutterFlowTheme.of(context).primaryBackground,
                                                  width: 2,
                                                ),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: FlutterFlowTheme.of(context).primaryBackground,
                                                  width: 2,
                                                ),
                                                borderRadius: BorderRadius.circular(14),
                                              ),
                                              filled: true,
                                              fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                                              contentPadding: EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                                            ),
                                            style: FlutterFlowTheme.of(context).bodyText1.override(
                                                  fontFamily: 'Sarabun',
                                                  fontSize: 16,
                                                  useGoogleFonts: false,
                                                ),
                                            keyboardType: TextInputType.phone,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 20,
                          ),

                          AnimatedContainer(
                            height: isAnimated ? 200 : 0,
                            duration: Duration(milliseconds: 300),
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        GestureDetector(
                                          onTap: () {
                                            print(isCountdown.toString());
                                            //pinNode!.requestFocus();
                                          },
                                          child: Text(
                                            'รหัสอ้างอิง ($refcode)',
                                            style: FlutterFlowTheme.of(context).bodyText1.override(
                                                  fontFamily: 'Sarabun',
                                                  color: Color(0xFF95A1AC),
                                                  fontSize: 15,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Center(
                                          child: Padding(
                                            padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 8),
                                            child: Text(
                                              'Shop Chill จะส่งรหัสยืนยันตัวตนให้คุณ\nผ่านทาง SMS ไปยัง ',
                                              textAlign: TextAlign.center,
                                              style: FlutterFlowTheme.of(context).subtitle2,
                                            ),
                                          ),
                                        ),
                                        animatedBorders()
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Padding(
                          //   padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //   child: Row(
                          //     mainAxisSize: MainAxisSize.max,
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     children: [
                          //       Expanded(
                          //         child: Column(
                          //           mainAxisSize: MainAxisSize.max,
                          //           crossAxisAlignment: CrossAxisAlignment.start,
                          //           children: [
                          //             Text(
                          //               'E-mail',
                          //               style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                     fontFamily: 'Sarabun',
                          //                     color: Colors.black,
                          //                     useGoogleFonts: false,
                          //                   ),
                          //             ),
                          //             Padding(
                          //               padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //               child: Container(
                          //                 width: double.infinity,
                          //                 decoration: BoxDecoration(),
                          //                 child: TextFormField(
                          //                   controller: emailAddressController,
                          //                   obscureText: false,
                          //                   decoration: InputDecoration(
                          //                     labelStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     labelText: '<EMAIL>',
                          //                     floatingLabelBehavior: FloatingLabelBehavior.never,
                          //                     hintStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     enabledBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     focusedBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     filled: true,
                          //                     fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                          //                     contentPadding: EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                          //                   ),
                          //                   style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                         fontFamily: 'Sarabun',
                          //                         fontSize: 16,
                          //                         useGoogleFonts: false,
                          //                       ),
                          //                   keyboardType: TextInputType.emailAddress,
                          //                 ),
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                          // Padding(
                          //   padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //   child: Row(
                          //     mainAxisSize: MainAxisSize.max,
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     children: [
                          //       Expanded(
                          //         child: Column(
                          //           mainAxisSize: MainAxisSize.max,
                          //           crossAxisAlignment: CrossAxisAlignment.start,
                          //           children: [
                          //             Text(
                          //               'รหัสผ่าน',
                          //               style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                     fontFamily: 'Sarabun',
                          //                     color: Colors.black,
                          //                     useGoogleFonts: false,
                          //                   ),
                          //             ),
                          //             Padding(
                          //               padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //               child: Container(
                          //                 width: double.infinity,
                          //                 decoration: BoxDecoration(),
                          //                 child: TextFormField(
                          //                   controller: passwordController,
                          //                   obscureText: !passwordVisibility,
                          //                   decoration: InputDecoration(
                          //                     labelStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     labelText: '**********',
                          //                     floatingLabelBehavior: FloatingLabelBehavior.never,
                          //                     hintStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     enabledBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     focusedBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     filled: true,
                          //                     fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                          //                     contentPadding: EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                          //                     suffixIcon: InkWell(
                          //                       onTap: () => setState(
                          //                         () => passwordVisibility = !passwordVisibility,
                          //                       ),
                          //                       focusNode: FocusNode(skipTraversal: true),
                          //                       child: Icon(
                          //                         passwordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                          //                         color: Color(0xFF757575),
                          //                         size: 22,
                          //                       ),
                          //                     ),
                          //                   ),
                          //                   style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                         fontFamily: 'Sarabun',
                          //                         fontSize: 16,
                          //                         useGoogleFonts: false,
                          //                       ),
                          //                 ),
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                          // Padding(
                          //   padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //   child: Row(
                          //     mainAxisSize: MainAxisSize.max,
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     children: [
                          //       Expanded(
                          //         child: Column(
                          //           mainAxisSize: MainAxisSize.max,
                          //           crossAxisAlignment: CrossAxisAlignment.start,
                          //           children: [
                          //             Text(
                          //               'ยืนยันรหัสผ่าน',
                          //               style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                     fontFamily: 'Sarabun',
                          //                     color: Colors.black,
                          //                     useGoogleFonts: false,
                          //                   ),
                          //             ),
                          //             Padding(
                          //               padding: EdgeInsetsDirectional.fromSTEB(0, 8, 0, 0),
                          //               child: Container(
                          //                 width: double.infinity,
                          //                 decoration: BoxDecoration(),
                          //                 child: TextFormField(
                          //                   controller: confirmPasswordController,
                          //                   obscureText: !confirmPasswordVisibility,
                          //                   decoration: InputDecoration(
                          //                     labelStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     labelText: '**********',
                          //                     floatingLabelBehavior: FloatingLabelBehavior.never,
                          //                     hintStyle: FlutterFlowTheme.of(context).bodyText2,
                          //                     enabledBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     focusedBorder: OutlineInputBorder(
                          //                       borderSide: BorderSide(
                          //                         color: FlutterFlowTheme.of(context).primaryBackground,
                          //                         width: 2,
                          //                       ),
                          //                       borderRadius: BorderRadius.circular(14),
                          //                     ),
                          //                     filled: true,
                          //                     fillColor: FlutterFlowTheme.of(context).backgroundTextForm,
                          //                     contentPadding: EdgeInsetsDirectional.fromSTEB(16, 16, 0, 16),
                          //                     suffixIcon: InkWell(
                          //                       onTap: () => setState(
                          //                         () => confirmPasswordVisibility = !confirmPasswordVisibility,
                          //                       ),
                          //                       focusNode: FocusNode(skipTraversal: true),
                          //                       child: Icon(
                          //                         confirmPasswordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                          //                         color: Color(0xFF757575),
                          //                         size: 22,
                          //                       ),
                          //                     ),
                          //                   ),
                          //                   style: FlutterFlowTheme.of(context).bodyText1.override(
                          //                         fontFamily: 'Sarabun',
                          //                         fontSize: 16,
                          //                         useGoogleFonts: false,
                          //                       ),
                          //                 ),
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                          // Padding(
                          //   padding: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 8),
                          //   child: ClipRRect(
                          //     borderRadius: BorderRadius.circular(4),
                          //     child: InkWell(
                          //       onTap: () async {
                          //         setState(() {
                          //           checkboxListTileValue = !checkboxListTileValue!;
                          //         });
                          //       },
                          //       splashColor: Colors.transparent,
                          //       highlightColor: Colors.transparent,
                          //       child: Row(
                          //         children: [
                          //           if (checkboxListTileValue!) ...[
                          //             Container(
                          //               width: 24,
                          //               height: 24,
                          //               decoration: BoxDecoration(
                          //                 gradient: LinearGradient(
                          //                   colors: [
                          //                     Color(0xFF0084F0),
                          //                     Color(0xFF0073E6),
                          //                     Color(0xFF0056D6),
                          //                   ],
                          //                   begin: AlignmentDirectional(0, -1),
                          //                   end: AlignmentDirectional(0, 1),
                          //                 ),
                          //                 shape: BoxShape.rectangle,
                          //                 borderRadius: BorderRadius.circular(4),
                          //               ),
                          //               alignment: AlignmentDirectional(0, 0),
                          //               child: FaIcon(
                          //                 FontAwesomeIcons.check,
                          //                 color: FlutterFlowTheme.of(context).secondaryBackground,
                          //                 size: 12,
                          //               ),
                          //             ),
                          //           ] else ...[
                          //             Container(
                          //               width: 24,
                          //               height: 24,
                          //               decoration: BoxDecoration(
                          //                 border: Border.all(color: Colors.grey),
                          //                 shape: BoxShape.rectangle,
                          //                 borderRadius: BorderRadius.circular(4),
                          //               ),
                          //               alignment: AlignmentDirectional(0, 0),
                          //               child: FaIcon(
                          //                 FontAwesomeIcons.check,
                          //                 color: FlutterFlowTheme.of(context).secondaryBackground,
                          //                 size: 12,
                          //               ),
                          //             ),
                          //           ],
                          //           Text(
                          //             '    ยอมรับเงื่อนไขและข้อกำหนด',
                          //             style: FlutterFlowTheme.of(context).title3.override(
                          //                   fontFamily: 'Sarabun',
                          //                   color: Colors.black,
                          //                   fontSize: 16,
                          //                   fontWeight: FontWeight.w400,
                          //                   useGoogleFonts: false,
                          //                 ),
                          //           ),
                          //         ],
                          //       ),
                          //     ),
                          //   ),
                          // ),
                          InkWell(
                              onTap: pinController.text.isEmpty || phoneController.text.isEmpty
                                  ? () {}
                                  : () async {
                                      ShopChillLoading.show();
                                      await LoginWithOtpCall.check(mobile: phoneController.text, otp: pinController.text).then((value) {
                                        print('val = $value');
                                        if (value['status'] == true) {
                                          CheckVerifyModel check = CheckVerifyModel.fromJson(value);
                                          ShopChillLoading.dismiss();
                                          Navigator.pushReplacement(context, CupertinoPageRoute(
                                            builder: (context) {
                                              return CheckDataLogin(
                                                checkVerifyModel: check,
                                              );
                                            },
                                          ));
                                        } else if (value['status'] == false && value['msg'] == 'Error check otp') {
                                          print('otp fail');
                                          ShopChillLoading.showError(value['msg']);
                                        } else if (value['status'] == false && value['msg'] == 'คุณยังไม่เป็นสมาชิก กรุณาสมัครสมาชิกก่อน') {
                                          ShopChillLoading.dismiss();
                                          Navigator.pushReplacement(context, CupertinoPageRoute(
                                            builder: (context) {
                                              return RegisterFullWidget(phone: phoneController.text);
                                            },
                                          ));
                                          print('register');
                                        } else if (value['status'] == false && value['msg'] == 'OTP Data Not Found') {
                                          print('otp fail');
                                          ShopChillLoading.showError(value['msg']);
                                        } else {
                                          ShopChillLoading.showError('error');
                                          print('error');
                                        }
                                      });
                                    },

                              // onTap: () async {
                              //   var _shouldSetState = false;
                              //   if (userNameController!.text.isEmpty) {
                              //     showError('กรุณากรอกชื่อผู้ใช้งาน');
                              //     return;
                              //   } else if (nameController!.text.isEmpty) {
                              //     showError('กรุณากรอกชื่อ-นามสกุล');
                              //     return;
                              //   } else if (!validateMobile(phoneController!.text, context)) {
                              //     return;
                              //   } else if (!validateEmail(emailAddressController!.text, context)) {
                              //     return;
                              //   } else if (passwordController!.text.length < 8) {
                              //     showError('รหัสผ่านต้องยาวอย่างน้อย 8 ตัวอักษร');
                              //     return;
                              //   } else if (passwordController!.text != confirmPasswordController!.text) {
                              //     showError('รหัสผ่านไม่ตรงกัน');
                              //     return;
                              //   } else if (!checkboxListTileValue!) {
                              //     showError('กรุณากดยอมรับเงื่อนไขและข้อกำหนด');
                              //     return;
                              //   }
                              //   showLoading();
                              //   var res = await RegisterCall().register(
                              //     name: nameController!.text,
                              //     username: userNameController!.text,
                              //     email: emailAddressController!.text,
                              //     phone: phoneController!.text,
                              //     password: passwordController!.text,
                              //     confirmPassword: confirmPasswordController!.text,
                              //   );
                              //   hideLoading();
                              //   _shouldSetState = true;
                              //   print('ress = $res');
                              //   if (res['success']) {
                              //     setState(() => FFAppState().token = res['data']['api_token']);
                              //     context.read<UserBloc>().add(GetUserProfile());
                              //     Navigator.pop(context, true);
                              //     Navigator.pop(context, true);
                              //     if (_shouldSetState) setState(() {});
                              //     return;
                              //   } else {
                              //     showError(RegisterCall.msg((registerReponse.jsonBody ?? '')).toString());
                              //     if (_shouldSetState) setState(() {});
                              //     return;
                              //   }
                              // },
                              child: Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                                child: Container(
                                  width: double.infinity,
                                  height: 60,
                                  decoration: pinController.text.isEmpty || phoneController.text.isEmpty
                                      ? BoxDecoration(
                                          color: Colors.grey,
                                          // image: DecorationImage(
                                          //   fit: BoxFit.cover,
                                          //   image: Image.asset(
                                          //     'assets/images/button_bg.png',
                                          //   ).image,
                                          // ),
                                          borderRadius: BorderRadius.circular(14),
                                        )
                                      : BoxDecoration(
                                          color: FlutterFlowTheme.of(context).secondaryBackground,
                                          image: DecorationImage(
                                            fit: BoxFit.cover,
                                            image: Image.asset(
                                              // 'assets/images/button_bg.png',
                                               UImageAssets.BUTTON_BG,
                                            ).image,
                                          ),
                                          borderRadius: BorderRadius.circular(14),
                                        ),
                                  child: Align(
                                    alignment: AlignmentDirectional(0, 0),
                                    child: Text(
                                      'สมัครสมาชิก',
                                      textAlign: TextAlign.center,
                                      style: FlutterFlowTheme.of(context).title2.override(
                                            fontFamily: 'Sarabun',
                                            color: Colors.white,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                ),
                              )),
                          Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 12),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AutoSizeText(
                                      'สมัครสมาชิกด้วย',
                                      textAlign: TextAlign.center,
                                      style: FlutterFlowTheme.of(context).subtitle1.override(
                                            fontFamily: 'Sarabun',
                                            color: Colors.black,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    InkWell(
                                      onTap: () async {
                                        final user = await signInWithFacebook(context);
                                        if (user == null) {
                                          return;
                                        }
                                        getLoginWithFacebook(user);
                                      },
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      child: Container(
                                        width: 50,
                                        height: 50,
                                        decoration: BoxDecoration(
                                          boxShadow: [
                                            BoxShadow(
                                              blurRadius: 5,
                                              color: Color(0x3314181B),
                                              offset: Offset(0, 2),
                                            )
                                          ],
                                          gradient: LinearGradient(
                                            colors: [Color(0xFF18ACFE), Color(0xFF0163E0)],
                                            stops: [0, 1],
                                            begin: AlignmentDirectional(0, -1),
                                            end: AlignmentDirectional(0, 1),
                                          ),
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: AlignmentDirectional(0, 0),
                                        child: FaIcon(
                                          FontAwesomeIcons.facebookF,
                                          color: FlutterFlowTheme.of(context).secondaryBackground,
                                          size: 24,
                                        ),
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () async {
                                        final user = await signInWithApple(context);
                                        if (user == null) {
                                          return;
                                        }
                                        getLoginWithApple(user);
                                      },
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      child: Container(
                                        width: 50,
                                        height: 50,
                                        decoration: BoxDecoration(
                                          color: Color(0xFF283544),
                                          boxShadow: [
                                            BoxShadow(
                                              blurRadius: 5,
                                              color: Color(0x3314181B),
                                              offset: Offset(0, 2),
                                            )
                                          ],
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: AlignmentDirectional(0, 0),
                                        child: FaIcon(
                                          FontAwesomeIcons.apple,
                                          color: FlutterFlowTheme.of(context).secondaryBackground,
                                          size: 24,
                                        ),
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () async {
                                        final user = await signInWithGoogle(context);
                                        if (user == null) {
                                          return;
                                        }
                                        getLoginWithGoogle(user);
                                      },
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      child: Container(
                                        width: 50,
                                        height: 50,
                                        decoration: BoxDecoration(
                                          image: DecorationImage(
                                            fit: BoxFit.cover,
                                            image: Image.asset(
                                              // 'assets/images/google.png',
                                              UImageAssets.GOOGLE,
                                            ).image,
                                          ),
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: AlignmentDirectional(0, 0),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(0, 50, 0, 24),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'หากคุณ มีบัญชีผู้ใช้แล้ว',
                                          style: FlutterFlowTheme.of(context).subtitle1.override(
                                                fontFamily: 'Sarabun',
                                                color: FlutterFlowTheme.of(context).black600,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'คุณสามารถ   ',
                                          style: FlutterFlowTheme.of(context).subtitle1.override(
                                                fontFamily: 'Sarabun',
                                                color: FlutterFlowTheme.of(context).black600,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                        FFButtonWidget(
                                          onPressed: () async {
                                            await Navigator.push(
                                              context,
                                              PageTransition(
                                                type: PageTransitionType.rightToLeft,
                                                duration: Duration(milliseconds: 0),
                                                reverseDuration: Duration(milliseconds: 0),
                                                child: LoginWidget(),
                                              ),
                                            );
                                          },
                                          text: 'เข้าสู่ระบบ !',
                                          options: FFButtonOptions(
                                            color: Color(0x00FFFFFF),
                                            textStyle: FlutterFlowTheme.of(context).subtitle1.override(
                                                  fontFamily: 'Sarabun',
                                                  color: FlutterFlowTheme.of(context).primaryColor,
                                                  useGoogleFonts: false,
                                                ),
                                            elevation: 0,
                                            borderSide: BorderSide(
                                              color: Colors.transparent,
                                              width: 1,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
 */*/

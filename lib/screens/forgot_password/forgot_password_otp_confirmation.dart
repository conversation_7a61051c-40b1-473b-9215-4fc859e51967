/* import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:pinput/pinput.dart';
import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/login/widgets/submit_button.dart';
import 'package:shop_chill_app/screens/register/register_widget.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/widgets/dialog/custom_alert_dialog.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_bg.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_logo.dart';
import 'package:smart_auth/smart_auth.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import 'reset_password_confirm_page.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';


class ForgotPasswordOtpConfirmation extends StatefulWidget {
  const ForgotPasswordOtpConfirmation({super.key, required this.mobile, required this.refCode});

  final String mobile;
  final String refCode;

  @override
  _ForgotPasswordOtpConfirmationState createState() => _ForgotPasswordOtpConfirmationState();
}

class _ForgotPasswordOtpConfirmationState extends State<ForgotPasswordOtpConfirmation> {
  late ApiCallResponse resetpasswordResponse;
  // late ApiCallResponse verifyOtpResponse;

  final _pinPutController = TextEditingController();
  final focusNode = FocusNode();
  final scaffoldKey = GlobalKey<ScaffoldState>();

  late Timer _timer;
  int _start = 60;
  String refCode = '';

  final smartAuth = SmartAuth.instance;

  final ValueNotifier<bool> isEnabledValue = ValueNotifier(false);

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(oneSec, (Timer timer) {
      if (_start == 0) {
        setState(() {
          timer.cancel();
        });
      } else {
        setState(() {
          _start--;
        });
      }
    });
  }

  void getAppSignature() async {
    final res = await smartAuth.getAppSignature();
    debugPrint('Signature: $res');
  }

  Future<void> userConsent() async {
    final res = await smartAuth.getSmsWithUserConsentApi();
    if (res.hasData) {
      final code = res.requireData.code;
      if (code != null) {
        _pinPutController.setText(code);
      }
    } else {
      debugPrint('User Consent failed or canceled: $res');
    }
  }

  Future<void> smsRetriever() async {
    final res = await smartAuth.getSmsWithRetrieverApi();
    if (res.hasData) {
      final code = res.requireData.code;
      if (code != null) {
        _pinPutController.setText(code);
      }
    } else {
      debugPrint('SMS Retriever failed: $res');
    }
  }

  @override
  void initState() {
    super.initState();
    getAppSignature();
    userConsent();
    startTimer();
    refCode = widget.refCode;

    _pinPutController.addListener(_validateInput);
  }

  void _validateInput() {
    final isValid = _pinPutController.text.isNotEmpty && _pinPutController.text.length == 6;
    isEnabledValue.value = isValid;
  }

  @override
  void dispose() {
    smartAuth.removeUserConsentApiListener();
    smartAuth.removeSmsRetrieverApiListener();
    _pinPutController.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        appBar: _buildAppBar(context),
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        body: ShopchillBg(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const ShopchillLogo(isHorizontal: true),
              20.gap,
              Text(
                'ระบบจะส่งรหัสยืนยันตัวตน 6 หลักให้คุณ\nผ่านทาง SMS ไปยังเบอร์โทรศัพท์ ${widget.mobile}',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              if (refCode.isNotEmpty && _start > 0) ...[
                Text('รหัสอ้างอิง ($refCode)', style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: ColorThemeConfig.unfocusedTextColor)),
              ],
              20.gap,
              animatedBorders(),
              10.gap,

              _buildResendSection(context),
              SubmitButton(isEnabled: isEnabledValue, onTap: () => _onComplete(_pinPutController.text), label: 'ถัดไป'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResendSection(BuildContext context) {
    return Expanded(
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_start == 0) ...[
            InkWell(
              onTap: () async {
                if (_start == 0) {
                  showLoading();
                  var shouldSetState = false;
                  resetpasswordResponse = await ResetPasswordCall.call(mobile: widget.mobile);
                  hideLoading();
                  shouldSetState = true;
                  if (ResetPasswordCall.status((resetpasswordResponse.jsonBody ?? ''))) {
                    startTimer();
                    if (shouldSetState) {
                      setState(() {
                        _start = 60;
                        refCode = ResetPasswordCall.refcode((resetpasswordResponse.jsonBody ?? ''));
                      });
                    }
                    _pinPutController.clear();
                    return;
                  } else {
                    showError(ResetPasswordCall.msg((resetpasswordResponse.jsonBody ?? '')).toString());
                    if (shouldSetState) setState(() {});
                    return;
                  }
                }
              },
              child: Text(
                'ส่งรหัสอีกครั้ง',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: ColorThemeConfig.primaryColor, fontSize: 15, fontWeight: FontWeight.w500),
              ),
            ),
          ] else ...[
            Text(
              'กรุณารอสักครู่',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: const Color(0xFF95A1AC), fontSize: 15, fontWeight: FontWeight.w500),
            ),
            Text(
              ' $_start ',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: const Color(0xFFFF2F2F), fontSize: 15, fontWeight: FontWeight.w500),
            ),
            Text(
              'วินาทีจะส่งได้อีกครั้ง',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: const Color(0xFF95A1AC), fontSize: 15, fontWeight: FontWeight.w500),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _onComplete(String pin) async {
    showLoading();

    try {
      final response = await VerifyOTPResetPasswordCall.callVerifySms(mobile: widget.mobile, otpCode: pin);

      hideLoading();

      if (response.status == true && response.code == '0000') {
        if (response.user?.apiToken == null) {
          return;
        }
        await Navigator.pushReplacement(
          context,
          CupertinoPageRoute(
            builder: (context) => ResetPasswordConfirmPage(mobile: widget.mobile, resetToken: response.resetPasswordToken ?? ''),
          ),
        );
        return;
      } else {
        setState(() {
          _pinPutController.clear();
        });
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext dContext) {
            return CustomAlertDialog(
              title: 'แจ้งเตือนจากระบบ',
              description: response.message ?? 'เกิดข้อผิดพลาดกรุณาลองใหม่',
              onCancel: () => Navigator.of(context, rootNavigator: true).pop(),
              btnConfirmText: 'ตกลง',
              onConfirm: () async {
                if (response.code == '1004') {
                  Navigator.of(dContext).pop();
                  Navigator.pushAndRemoveUntil(
                    context,
                    CupertinoPageRoute(builder: (context) => const RegisterPage()),
                    (route) =>
                        route.settings.name == AppRoutes.navBar || route.settings.name == AppRoutes.home || route.settings.name == AppRoutes.login,
                  );
                } else {
                  Navigator.of(context, rootNavigator: true).pop();
                }
              },
            );
          },
        );
      }
    } catch (e, stackTrace) {
      hideLoading();
      debugPrintStack(label: 'Error: $e', stackTrace: stackTrace);
    } finally {
      hideLoading();
    }
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('กรอกรหัสยืนยันตัวตน', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),
      centerTitle: true,
      elevation: 0,
    );
  }

  Widget animatedBorders() {
    const length = 6;
    const borderColor = Color.fromRGBO(114, 178, 238, 1);
    const errorColor = Color.fromRGBO(255, 234, 238, 1);
    const fillColor = Color.fromRGBO(222, 231, 240, .57);
    final defaultPinTheme = PinTheme(
      width: 55,
      height: 65,
      textStyle: GoogleFonts.poppins(fontSize: 22, color: const Color.fromRGBO(30, 60, 87, 1)),
      decoration: BoxDecoration(
        color: fillColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.transparent),
      ),
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: SizedBox(
        height: 60,
        child: Pinput(
          length: length,
          controller: _pinPutController,
          focusNode: focusNode,
          defaultPinTheme: defaultPinTheme,
          onChanged: (value) {
            setState(() {});
          },
          onCompleted: (pin) {},
          focusedPinTheme: defaultPinTheme.copyWith(
            height: 55,
            width: 60,
            decoration: defaultPinTheme.decoration!.copyWith(border: Border.all(color: borderColor)),
          ),
          errorPinTheme: defaultPinTheme.copyWith(
            decoration: BoxDecoration(color: errorColor, borderRadius: BorderRadius.circular(8)),
          ),
        ),
      ),
    );
  }
}
 */
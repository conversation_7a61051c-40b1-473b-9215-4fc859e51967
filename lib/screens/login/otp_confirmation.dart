import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';
import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/forgot_password/reset_password_confirm_page.dart';
import 'package:shop_chill_app/screens/home/<USER>/home_bloc.dart';
import 'package:shop_chill_app/screens/login/cubit/timer_cubit.dart';
import 'package:shop_chill_app/screens/login/cubit/timer_state.dart';
import 'package:shop_chill_app/screens/login/utils/sms_retriever_impl.dart';
import 'package:shop_chill_app/screens/login/widgets/submit_button.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/order/bloc/order_count_cubit/order_count_cubit.dart';
import 'package:shop_chill_app/screens/register/check_data_login.dart';
import 'package:shop_chill_app/screens/register/models/check_verify_model.dart';
import 'package:shop_chill_app/screens/register/models/provider_auth_response.dart';
import 'package:shop_chill_app/screens/register/register_form_page.dart';
import 'package:shop_chill_app/screens/register/register_paget.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/widgets/dialog/custom_alert_dialog.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_bg.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_logo.dart';
import '../../config/shopchill_loading/shopchill_loading.dart';
import '../flutter_flow/flutter_flow_util.dart';
import '../notification/fcm/fcm_bloc.dart';

enum ConfirmationOtpType { register, registerWithProvider, loginWhithOTP, forgotPassword }

class OtpConfirmation extends StatefulWidget {
  final String mobile;
  final String refCode;
  final ConfirmationOtpType type;
  final ProviderAuthResponse? providerAuthResponse;
  const OtpConfirmation({super.key, required this.mobile, required this.refCode, required this.type, this.providerAuthResponse});

  @override
  State<OtpConfirmation> createState() => _OtpConfirmationtate();
}

class _OtpConfirmationtate extends State<OtpConfirmation> {
  final TextEditingController _pinController = TextEditingController();

  final ValueNotifier<bool> isEnabledValue = ValueNotifier(false);
  final focusNode = FocusNode();
  String refCode = '';

  @override
  void initState() {
    super.initState();
    refCode = widget.refCode;
    _pinController.addListener(_validateInput);
  }

  void _validateInput() {
    final isValid = _pinController.text.isNotEmpty && _pinController.text.length == 6;
    isEnabledValue.value = isValid;
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }

  String _getButtomNameByType() {
    switch (widget.type) {
      case ConfirmationOtpType.register:
        return 'กรอกรหัส';
      case ConfirmationOtpType.registerWithProvider:
        return 'กรอกรหัส';
      case ConfirmationOtpType.loginWhithOTP:
        return 'กรอกรหัส';
      case ConfirmationOtpType.forgotPassword:
        return 'กรอกรหัส';
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        appBar: _buildAppBar(context),
        body: ShopchillBg(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              const ShopchillLogo(isHorizontal: true),
              20.gap,
              Text(
                'ระบบจะส่งรหัสยืนยันตัวตน 6 หลักให้คุณ\nผ่านทาง SMS ไปยังเบอร์โทรศัพท์ ${widget.mobile}',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              _buildRefCodeSection(),
              20.gap,
              Expanded(child: Column(children: [animatedBorders(), 10.gap, _buildResendSection()])),

              SubmitButton(
                isEnabled: isEnabledValue,
                label: _getButtomNameByType(),
                onTap: () async {
                  await _onComplete(_pinController.text);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('กรอกรหัสยืนยันตัวตน', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),
      centerTitle: true,
      elevation: 0,
    );
  }

  Widget _buildRefCodeSection() {
    return BlocBuilder<WorkoutCubit, WorkoutState>(
      builder: (context, state) {
        return refCode.isNotEmpty && state is WorkoutInProgress
            ? Text('อ้างอิง ($refCode)', style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: ColorThemeConfig.unfocusedTextColor))
            : const SizedBox();
      },
    );
  }

  Widget _buildResendSection() {
    return BlocBuilder<WorkoutCubit, WorkoutState>(
      builder: (context, state) {
        if (state is WorkoutInitial) {
          return GestureDetector(
            onTap: () {
              _onResendOTP();
            },
            child: Text(
              'ส่งอีกครั้ง',
              style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.blue, fontSize: 15, fontWeight: FontWeight.bold),
            ),
          );
        } else if (state is WorkoutInProgress) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'กรุณารอสักครู่',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: const Color(0xFF95A1AC), fontSize: 15, fontWeight: FontWeight.w500),
              ),
              Text(
                ' ${state.elapsed} ',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: const Color(0xFFFF2F2F), fontSize: 15, fontWeight: FontWeight.w500),
              ),
              Text(
                'วินาทีจะส่งได้อีกครั้ง',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: const Color(0xFF95A1AC), fontSize: 15, fontWeight: FontWeight.w500),
              ),
            ],
          );
        } else {
          return const Text('มีบางอย่างผิดพลาด');
        }
      },
    );
  }

  Widget animatedBorders() {
    const length = 6;
    const borderColor = Color.fromRGBO(114, 178, 238, 1);
    const errorColor = Color.fromRGBO(255, 234, 238, 1);
    const fillColor = Color.fromRGBO(222, 231, 240, .57);
    final defaultPinTheme = PinTheme(
      width: 55,
      height: 65,
      textStyle: GoogleFonts.poppins(fontSize: 22, color: const Color.fromRGBO(30, 60, 87, 1)),
      decoration: BoxDecoration(
        color: fillColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.transparent),
      ),
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: SizedBox(
        height: 60,
        child: Pinput(
          length: length,
          controller: _pinController,
          smsRetriever: SmsRetrieverImpl(),
          focusNode: focusNode,
          defaultPinTheme: defaultPinTheme,
          onChanged: (value) {
            setState(() {});
          },
          onCompleted: (pin) {
            focusNode.unfocus();
            _onComplete(pin);

            setState(() {});
          },
          focusedPinTheme: defaultPinTheme.copyWith(
            height: 55,
            width: 60,
            decoration: defaultPinTheme.decoration!.copyWith(border: Border.all(color: borderColor)),
          ),
          errorPinTheme: defaultPinTheme.copyWith(
            decoration: BoxDecoration(color: errorColor, borderRadius: BorderRadius.circular(8)),
          ),
        ),
      ),
    );
  }

  void _onResendOTP() async {
    showLoading();
    try {
      switch (widget.type) {
        case ConfirmationOtpType.register:
        case ConfirmationOtpType.registerWithProvider:
          await RegisterCall().registerOtp(phone: widget.mobile).then((value) {
            hideLoading();
            if (value['status'] == true) {
              BlocProvider.of<WorkoutCubit>(context).startWorkout(0);

              ShopChillLoading.showToast('ส่ง OTP เรียบร้อย');
              setState(() {
                refCode = value['ref_code'];
              });
            }
          });
          break;
        case ConfirmationOtpType.loginWhithOTP:
          await RegisterCall().registerOtp(phone: widget.mobile).then((value) {
            hideLoading();
            if (value['status'] == true) {
              BlocProvider.of<WorkoutCubit>(context).startWorkout(0);
              ShopChillLoading.showToast('ส่ง OTP เรียบร้อย');
              setState(() {
                refCode = value['ref_code'];
              });
            }
          });
          break;
        case ConfirmationOtpType.forgotPassword:
          await ResetPasswordCall.call(mobile: widget.mobile).then((value) {
            hideLoading();
            if (ResetPasswordCall.status((value.jsonBody ?? ''))) {
              BlocProvider.of<WorkoutCubit>(context).startWorkout(0);

              ShopChillLoading.showToast('ส่ง OTP เรียบร้อย');
              setState(() {
                refCode = ResetPasswordCall.refcode((value.jsonBody ?? ''));
              });
            }
            ShopChillLoading.showToast('ส่ง OTP เรียบร้อย');
          });
          break;
      }
    } catch (e, stackTrace) {
      debugPrintStack(label: 'Error: $e', stackTrace: stackTrace);
    } finally {
      hideLoading();
    }
  }

  Future<void> _onComplete(String pin) async {
    showLoading();

    try {
      /* error code :
          1004 - ไม่พบข้อมูล ผู้ใช้งาน - prefer register form
          2001 - รหัสอ้างอิง หมดอายุ
          2002 - พบข้อผิดพลาด OTP
          2003 - ไม่สามารถตรวจสอบ OTP ได้
          2004 - ไม่พบ request OTP ของการทำงาน

          success code :
          0000 - ดำเนินการเรียบร้อย - prefer login user from data response */

      switch (widget.type) {
        case ConfirmationOtpType.register:
        case ConfirmationOtpType.registerWithProvider:
          _onRegisterProcess(pin);
          break;
        case ConfirmationOtpType.loginWhithOTP:
          _onLoginProcess(pin);
          break;
        case ConfirmationOtpType.forgotPassword:
          _onForgotPasswordProcess(pin);
          break;
      }
    } catch (e, stackTrace) {
      hideLoading();
      debugPrintStack(label: 'Error: $e', stackTrace: stackTrace);
    } finally {
      hideLoading();
    }
  }

  void _onLoginProcess(String pin) async {
    final String? fcmToken = await getFcmToken();

    final CheckVerifyResponseModel response = await LoginWithOtpCall.callVerifySms(mobile: widget.mobile, otp: pin, fcmToken: fcmToken ?? '');
    hideLoading();
    _pinController.clear();
    if (response.status == true && response.code == '0000') {
      if (response.user?.apiToken == null) {
        return;
      }
      setState(() => FFAppState().token = response.user?.apiToken ?? '');

      context.read<CartBloc>().add(GetCartEvent());

      context.read<UserBloc>().add(const GetUserProfile());

      context.read<OrderCountCubit>().fetchOrderCount();
      context.read<HomeBloc>().add(GetHomeEvent(context));

      Navigator.of(context).popUntil((route) => route.isFirst);
      return;
    } else {
      _showMessageDialog(response);
    }
  }

  void _onRegisterProcess(String pinCode) async {
    final CheckVerifyResponseModel response = await LoginWithOtpCall.callVerifySms(mobile: widget.mobile, otp: pinCode, fcmToken: null);

    hideLoading();
    _pinController.clear();

    if (response.status == true && response.code == '0000') {
      if (response.user != null) {
        Navigator.pushReplacement(
          context,
          CupertinoPageRoute(
            builder: (context) {
              return CheckDataLogin(userData: response.user!);
            },
          ),
        );
      }
    } else if (response.code == '1004') {
      Navigator.pushReplacement(
        context,
        CupertinoPageRoute(
          builder: (context) {
            return RegisterFormPage(phone: widget.mobile, isProviderLogin: widget.type == ConfirmationOtpType.registerWithProvider);
          },
        ),
      );
    } else {
      _showMessageDialog(response);
    }
  }

  void _onForgotPasswordProcess(String pin) async {
    final response = await VerifyOTPResetPasswordCall.callVerifySms(mobile: widget.mobile, otpCode: pin);

    hideLoading();
    _pinController.clear();

    if (response.status == true && response.code == '0000') {
      if (response.user?.apiToken == null) {
        return;
      }
      await Navigator.pushReplacement(
        context,
        CupertinoPageRoute(
          builder: (context) => ResetPasswordConfirmPage(mobile: widget.mobile, resetToken: response.resetPasswordToken ?? ''),
        ),
      );
      return;
    } else {
      _showMessageDialog(response);
    }
  }

  void _showMessageDialog(CheckVerifyResponseModel response) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dContext) {
        return CustomAlertDialog(
          title: 'แจ้งเตือนจากระบบ',
          description: response.message ?? 'เกิดข้อผิดพลาดกรุณาลองใหม่',
          onCancel: () => Navigator.of(context, rootNavigator: true).pop(),
          btnConfirmText: 'ตกลง',
          onConfirm: () async {
            if (response.code == '1004') {
              Navigator.of(dContext).pop();
              Navigator.pushAndRemoveUntil(
                context,
                CupertinoPageRoute(builder: (context) => const RegisterPage()),
                (route) => route.settings.name == AppRoutes.navBar || route.settings.name == AppRoutes.home || route.settings.name == AppRoutes.login,
              );
            } else {
              Navigator.of(context, rootNavigator: true).pop();

              _onResendOTP();
            }
          },
        );
      },
    );
  }
}

import 'package:shop_chill_app/screens/my_account/models/partner_affiliate_user.dart';

class UserModel {
  UserModel({required this.status, required this.message, required this.data});

  final bool? status;
  final String? message;
  final Data? data;

  UserModel copyWith({bool? status, String? message, Data? data}) {
    return UserModel(status: status ?? this.status, message: message ?? this.message, data: data ?? this.data);
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(status: json["status"] ?? false, message: (json["message"] ?? '').toString(), data: json["data"] == null ? null : Data.fromJson(json["data"]));
  }
}

class Data {
  User? user;
  PartnerAffiliateUser? partnerAffiliateUser;

  Data({this.user, this.partnerAffiliateUser});

  Data.fromJson(Map<String, dynamic> json) {
    user = json['user'] != null ? User.from<PERSON>son(json['user']) : null;
    partnerAffiliateUser = json['partner_affiliate_user'] != null ? PartnerAffiliateUser.fromJson(json['partner_affiliate_user']) : null;
  }
}

class User {
  User({
    required this.id,
    required this.name,
    required this.email,
    required this.emailVerifiedAt,
    required this.username,
    required this.avatar,
    required this.phone,
    required this.phoneVerifiedAt,
    required this.apiToken,
    required this.onetimeLoginToken,
    required this.resetPasswordToken,
    required this.ishipId,
    required this.ishipToken,
    required this.facebookId,
    required this.facebookName,
    required this.facebookToken,
    required this.facebookStatus,
    required this.lineId,
    required this.lineName,
    required this.lineToken,
    required this.lineStatus,
    required this.googleId,
    required this.googleName,
    required this.googleToken,
    required this.googleStatus,
    required this.appleId,
    required this.appleName,
    required this.appleToken,
    required this.appleStatus,
    required this.status,
    required this.userStatus,
    required this.deletedAt,
    required this.createdAt,
    required this.updatedAt,
    required this.following,
    required this.fcmTokens,
  });

  var id;
  var name;
  var email;
  var emailVerifiedAt;
  var username;
  var avatar;
  var phone;
  var phoneVerifiedAt;
  var apiToken;
  var onetimeLoginToken;
  var resetPasswordToken;
  var ishipId;
  var ishipToken;
  var facebookId;
  var facebookName;
  var facebookToken;
  var facebookStatus;
  var lineId;
  var lineName;
  var lineToken;
  var lineStatus;
  var googleId;
  var googleName;
  var googleToken;
  var googleStatus;
  var appleId;
  var appleName;
  var appleToken;
  var appleStatus;
  var status;
  var userStatus;
  var deletedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? following;
  List<String?> fcmTokens;

  User copyWith({
    var id,
    var name,
    var email,
    var emailVerifiedAt,
    var username,
    var avatar,
    var phone,
    var phoneVerifiedAt,
    var apiToken,
    var onetimeLoginToken,
    var resetPasswordToken,
    var ishipId,
    var ishipToken,
    var facebookId,
    var facebookName,
    var facebookToken,
    var facebookStatus,
    var lineId,
    var lineName,
    var lineToken,
    var lineStatus,
    var googleId,
    var googleName,
    var googleToken,
    var googleStatus,
    var appleId,
    var appleName,
    var appleToken,
    var appleStatus,
    var status,
    var userStatus,
    var deletedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? following,
    List<String?>? fcmTokens,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      username: username ?? this.username,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
      apiToken: apiToken ?? this.apiToken,
      onetimeLoginToken: onetimeLoginToken ?? this.onetimeLoginToken,
      resetPasswordToken: resetPasswordToken ?? this.resetPasswordToken,
      ishipId: ishipId ?? this.ishipId,
      ishipToken: ishipToken ?? this.ishipToken,
      facebookId: facebookId ?? this.facebookId,
      facebookName: facebookName ?? this.facebookName,
      facebookToken: facebookToken ?? this.facebookToken,
      facebookStatus: facebookStatus ?? this.facebookStatus,
      lineId: lineId ?? this.lineId,
      lineName: lineName ?? this.lineName,
      lineToken: lineToken ?? this.lineToken,
      lineStatus: lineStatus ?? this.lineStatus,
      googleId: googleId ?? this.googleId,
      googleName: googleName ?? this.googleName,
      googleToken: googleToken ?? this.googleToken,
      googleStatus: googleStatus ?? this.googleStatus,
      appleId: appleId ?? this.appleId,
      appleName: appleName ?? this.appleName,
      appleToken: appleToken ?? this.appleToken,
      appleStatus: appleStatus ?? this.appleStatus,
      status: status ?? this.status,
      userStatus: userStatus ?? this.userStatus,
      deletedAt: deletedAt ?? this.deletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      following: following ?? this.following,
      fcmTokens: fcmTokens ?? this.fcmTokens,
    );
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json["id"],
      name: json["name"],
      email: json["email"],
      emailVerifiedAt: json["email_verified_at"],
      username: json["username"],
      avatar: json["avatar"],
      phone: json["phone"],
      phoneVerifiedAt: json["phone_verified_at"],
      apiToken: json["api_token"],
      onetimeLoginToken: json["onetime_login_token"],
      resetPasswordToken: json["reset_password_token"],
      ishipId: json["iship_id"],
      ishipToken: json["iship_token"],
      facebookId: json["facebook_id"],
      facebookName: json["facebook_name"],
      facebookToken: json["facebook_token"],
      facebookStatus: json["facebook_status"],
      lineId: json["line_id"],
      lineName: json["line_name"],
      lineToken: json["line_token"],
      lineStatus: json["line_status"],
      googleId: json["google_id"],
      googleName: json["google_name"],
      googleToken: json["google_token"],
      googleStatus: json["google_status"],
      appleId: json["apple_id"],
      appleName: json["apple_name"],
      appleToken: json["apple_token"],
      appleStatus: json["apple_status"],
      status: json["status"],
      userStatus: json["user_status"],
      deletedAt: json["deleted_at"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      following: int.tryParse((json["following"] ?? 0).toString()) ?? 0,
      fcmTokens: json["fcm_tokens"] == null ? [] : List<String?>.from(json["fcm_tokens"]!.map((x) => x)),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "email": email,
    "email_verified_at": emailVerifiedAt,
    "username": username,
    "avatar": avatar,
    "phone": phone,
    "phone_verified_at": phoneVerifiedAt,
    "api_token": apiToken,
    "onetime_login_token": onetimeLoginToken,
    "reset_password_token": resetPasswordToken,
    "iship_id": ishipId,
    "iship_token": ishipToken,
    "facebook_id": facebookId,
    "facebook_name": facebookName,
    "facebook_token": facebookToken,
    "facebook_status": facebookStatus,
    "line_id": lineId,
    "line_name": lineName,
    "line_token": lineToken,
    "line_status": lineStatus,
    "google_id": googleId,
    "google_name": googleName,
    "google_token": googleToken,
    "google_status": googleStatus,
    "apple_id": appleId,
    "apple_name": appleName,
    "apple_token": appleToken,
    "apple_status": appleStatus,
    "status": status,
    "user_status": userStatus,
    "deleted_at": deletedAt,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "following": following,
    "fcm_tokens": fcmTokens,
  };
}

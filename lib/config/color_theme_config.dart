import 'package:flutter/material.dart';

class ColorThemeConfig {
  ColorThemeConfig._();
  static const Color primaryColor = Color(0xFF0056D6);
  static const Color shopColor = Color(0xFFFF5C02);
  static const Color newPrimaryColor = Color(0xFF0056D6); //Color(0xFF1877F2);
  static const Color fillColor = Color(0xFFF2F8FF);
  static const Color textButtonColor = Color(0xFF62A0FD);
  static const Color secondaryColor = Color(0xFF39D2C0);
  static const Color tertiaryColor = Color(0xFFEE8B60);
  static const Color alternate = Color(0xFFFF5963);
  static const Color primaryBackground = Color(0xFFF0F0F0);
  static const Color primaryText = Color(0xFF3E4958);
  static const Color secondaryText = Color(0xFF57636C);
  static const Color shippingFeePrimaryColor = Color(0xFF26AA9A);
  static const Color shippingFeeSecondaryColor = Color(0xFFF7FDFB);
  static const Color followShopColor = Color(0xFFBA0F19);
  static const Color gflashSalePrimaryColor = Color(0xFFBF100D);
  static const Color gflashSaleSecondaryColor = Color(0xFFE50E0B);
  static const Color flashSalePrimaryColor = Color(0xFFEE3D3A);
  static const Color flashSaleSecondaryColor = Color(0xFFFFE9E9);
  static const Color priceColor = Color(0xFFE90D0A);
  static const Color flashSaleTabColor = Color(0xFFE90D0A);
  static const Color greenActive = Color(0xFF27CE46);
  static const Color borderProductCardColor = Color(0xFFE4E4E4);
  static const Color starColor = Color(0xFFF5B100);

  static const Color gMallPrimaryColor = Color(0xFFBB0C09);
  static const Color gMallSecondaryColor = Color(0xFFEA1713);

  static const List<Color> flashSaleGradientColor = [gflashSalePrimaryColor, gflashSaleSecondaryColor];
  static const List<Color> mallGradientColor = [gMallPrimaryColor, gMallSecondaryColor];

  static const Color incomeText = Color(0xFFFF5C02);

  static const Color primaryBtnText = Color(0xFFFFFFFF);
  static const Color lineColor = Color(0xFFE0E3E7);
  static const Color grayIcon = Color(0xFF95A1AC);
  static const Color grayIcon2 = Color(0xFFC2C2C2);
  static const Color gray200 = Color(0xFFDBE2E7);
  static const Color gray600 = Color(0xFF262D34);
  static const Color black600 = Color(0xFF090F13);
  static const Color tertiary400 = Color(0xFF39D2C0);
  static const Color textColor = Color(0xFF1E2429);
  static const Color btnText = Color(0xFFFFFFFF);
  static const Color customColor3 = Color(0xFFDF3F3F);
  static const Color customColor4 = Color(0xFF090F13);
  static const Color white = Color(0xFFFFFFFF);
  static const Color background = Color(0xFF1D2429);
  static const Color backgroundComponents = Color(0xFF1D2428);
  static const Color backgroundTextForm = Color(0xFFF7F8F9);
  static const Color unfocusedTextColor = Color(0xFF929292);
  static const Color primaryBtnActiveColor = Color(0xFF0056D6); //Color(0xff0084F0);
  static const Color primaryBtnInactiveColor = Color(0xFF0056D6); //Color(0xff0084F0);
  // static const Color primaryBtnActiveColor = Color(0xFF0056D6); //Color(0xff0084F0);
}

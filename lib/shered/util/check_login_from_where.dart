enum ProviderType { google, facebook, apple, line, others }

extension ProviderTypeExtension on ProviderType {
  String get name => toString().replaceAll(RegExp(r".*\."), "");
}

class CheckProviderType {
  static ProviderType checkProviderType(String providerId) {
    if (providerId == 'google.com') {
      return ProviderType.google;
    } else if (providerId == 'facebook.com') {
      return ProviderType.facebook;
    } else if (providerId == 'apple.com') {
      return ProviderType.apple;
    } else if (providerId == 'line.com') {
      return ProviderType.line;
    } else {
      return ProviderType.others;
    }
  }

  static String ProviderTypeName(ProviderType loginFromWhere) {
    switch (loginFromWhere) {
      case ProviderType.google:
        return 'Google';
      case ProviderType.facebook:
        return 'Facebook';
      case ProviderType.apple:
        return 'Apple';
      case ProviderType.line:
        return 'Line';
      case ProviderType.others:
        return '';
    }
  }
}

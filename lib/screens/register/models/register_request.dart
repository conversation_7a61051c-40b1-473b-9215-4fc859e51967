class RegisterRequest {
  final String username;
  final String name;
  final String phone;
  final String email;
  final String? fcmToken;
  final String? password;
  final String? passwordConfirmation;
  final String? avatar;
  final String? provider;
  final String? providerId;
  final String? providerName;
  final String? providerToken;

  const RegisterRequest({
    required this.username,
    required this.name,
    required this.phone,
    required this.email,
    this.fcmToken,
    this.password,
    this.passwordConfirmation,
    this.avatar,
    this.provider,
    this.providerId,
    this.providerName,
    this.providerToken,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) {
    return RegisterRequest(
      username: json['username'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '', // **********
      email: json['email'] ?? '', // <EMAIL>
      fcmToken: json['fcm_token'] ?? '', // fcm_token
      password: json['password'] ?? '', // **********
      passwordConfirmation: json['password_confirmation'] ?? '', // **********
      avatar: json['avatar'] ?? '', // xxxxxx
      provider: json['provider'] ?? '', // facebook
      providerId: json['provider_id'] ?? '', // **********
      providerName: json['provider_name'] ?? '', // Facebook Facebook
      providerToken: json['provider_token'] ?? '', // **********
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'name': name,
      'phone': phone,
      'email': email,
      if (fcmToken != null && fcmToken!.isNotEmpty) 'fcm_token': fcmToken,
      if (password != null && password!.isNotEmpty) 'password': password,
      if (passwordConfirmation != null && passwordConfirmation!.isNotEmpty) 'password_confirmation': passwordConfirmation,
      if (avatar != null && avatar!.isNotEmpty) 'avatar': avatar,
      if (provider != null && provider!.isNotEmpty) 'provider': provider,
      if (providerId != null && providerId!.isNotEmpty) 'provider_id': providerId,
      if (providerName != null && providerName!.isNotEmpty) 'provider_name': providerName,
      if (providerToken != null && providerToken!.isNotEmpty) 'provider_token': providerToken,
    };
  }
}

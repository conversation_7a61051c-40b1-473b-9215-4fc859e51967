import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/api_end_point.dart';

import 'package:http/http.dart' as http;
import 'package:shop_chill_app/backend/api_requests/api_end_point_v2.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/register/models/check_verify_model.dart';

import 'api_manager.dart';
import 'domain.dart';

export 'api_manager.dart' show ApiCallResponse;

class LoginCall {
  static Future<ApiCallResponse> call({String? phone = '', String? password = '', String? fcmToken = ''}) {
    final body =
        '''
{
  "phone": "$phone",
  "password": "$password",
  "fcm_token": "$fcmToken"
}''';

    print(body);
    return ApiManager.instance.makeApiCall(
      callName: 'login',
      apiUrl: ApiEndPoint.LOGIN,
      callType: ApiCallType.POST,
      headers: {},
      params: {'phone': phone, 'password': password, "fcm_token": "$fcmToken"},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic message(dynamic response) => getJsonField(response, r'''$.message''');

  static dynamic token(dynamic response) => getJsonField(response, r'''$.token''');
}

class LoginWithFacebookCall {
  static Future<ApiCallResponse> call({
    String? name = '',
    String? email = '',
    String? id = '',
    String? token = '',
    String? avatar = '',
    String? fcmToken = '',
  }) {
    final body =
        '''{
    "name": "$name",
    "email": "$email",
    "id":"$id",
    "token": "$token",
    "fcm_token": "$fcmToken",
    "avatar":"$avatar"
}''';

    return ApiManager.instance.makeApiCall(
      callName: 'Login with Facebook',
      apiUrl: ApiEndPoint.LOGIN_FACEBOOK,
      callType: ApiCallType.POST,
      headers: {},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic message(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic token(dynamic response) => getJsonField(response, r'''$.data.api_token''');
}

class StoreFCM {
  static Future storefcm({required String? fcm}) async {
    final headers = {'Content-Type': 'application/json', 'Authorization': 'Bearer ${FFAppState().token}'};
    final request = http.Request('POST', Uri.parse('${domain.domain}/api/user/fcm-update'));
    request.body = json.encode({"fcm_token": fcm});
    request.headers.addAll(headers);

    final http.StreamedResponse response = await request.send();

    if (response.statusCode == 200) {
      final responseBody = await response.stream.bytesToString();
      print('store res = $responseBody');
    } else {
      print(response.reasonPhrase);
    }
  }
}

class LoginWithOtpCall {
  static Future<CheckVerifyResponseModel> callVerifySms({required String mobile, required String otp, required String? fcmToken}) async {
    try {
      final headers = {'Content-Type': 'application/json'};
      final request = http.Request('POST', Uri.parse('${domain.domain}${ApiEndPointV2.LOGIN_OTP}'));
      final body = {"mobile": mobile, "otpCode": otp, if (fcmToken != null) "fcm_token": fcmToken};
      request.body = json.encode(body);
      request.headers.addAll(headers);

      final http.StreamedResponse response = await request.send();
      if (response.statusCode == 200) {
        final responseString = await response.stream.bytesToString();

        final res = jsonDecode(responseString);
        return CheckVerifyResponseModel.fromJson(res);
      } else {
        return CheckVerifyResponseModel(status: false, code: '', message: 'เกิดข้อผลพลาด ลองใหม่ครั้ง', data: null, onetimeToken: '', user: null);
      }
    } catch (e, stackTrace) {
      debugPrintStack(label: 'Error: $e', stackTrace: stackTrace);
      return CheckVerifyResponseModel(status: false, code: '', message: 'เกิดข้อผลพลาด ลองใหม่ครั้ง', data: null, onetimeToken: '', user: null);
    }
  }

  /*   static Future check({required String mobile, required String otp}) async {
    try {
      final headers = {'Content-Type': 'application/json'};
      final request = http.Request('POST', Uri.parse(ApiEndPointV2.LOGIN_OTP));
      request.body = json.encode({"mobile": mobile, "otpCode": otp});
      request.headers.addAll(headers);

      final http.StreamedResponse response = await request.send();
      if (response.statusCode == 200) {
        final responseString = await response.stream.bytesToString();
        print('Response: $responseString');

        final res = jsonDecode(responseString);
        print('Decoder: $res');

        return res;
      } else {
        print('Error: ${response.reasonPhrase}');
      }
    } catch (e, stackTrace) {
      print('Login OTP exception: $e');
      print(stackTrace);
    }
  } */
}

class LoginWithGoogleCall {
  static Future<ApiCallResponse> call({
    String? name = '',
    String? email = '',
    String? id = '',
    String? token = '',
    String? avatarOriginal = '',
    String? fcmToken = '',
  }) {
    final body =
        '''{
    "name": "$name",
    "email": "$email",
    "id":"$id",
    "fcm_token": "$fcmToken",
    "token": "$token",
    "avatar":"$avatarOriginal"
}''';
    print("body : $body");
    return ApiManager.instance.makeApiCall(
      callName: 'Login with Google',
      // apiUrl: ApiEndPoint.LOGIN_GOOGLE,
      apiUrl: '${domain.domain}${ApiEndPointV2.LOGIN_GOOGLE}',
      callType: ApiCallType.POST,
      headers: {},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic message(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic token(dynamic response) => getJsonField(response, r'''$.data.api_token''');
}

class LoginWithAppleCall {
  static Future<ApiCallResponse> call({
    String? name = '',
    String? email = '',
    String? id = '',
    String? token = '',
    String? phone = '',
    String? username = '',
    String? fcmToken = '',
  }) {
    final body =
        '''{
    "token":"$token",
    "id":"$id",
    "email": "$email",
    "name": "$name",
    "username":"$username",
    "phone":"$phone",
    "fcm_token":"$fcmToken"
}''';
    /*  final body = '''{
    "name": "$name",
    "email": "$email",
    "apple_id":"$id",
    "apple_token":"$token",
    "phone":"$phone",
    "username":"$username",
    "fcm_token":"$fcmToken"
}'''; */

    print(body);

    return ApiManager.instance.makeApiCall(
      callName: 'Login with Apple',
      apiUrl: ApiEndPoint.LOGIN_APPLE,
      callType: ApiCallType.POST,
      headers: {},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic message(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic token(dynamic response) => getJsonField(response, r'''$.data.api_token''');
}

class LoginWithLineCall {
  static Future<ApiCallResponse> call({String? id = '', String? name = '', String? email = '', String? avatar = '', String? token = ''}) {
    final body =
        '''{
    "id":"$id",
    "name": "$name",
    "email": "$email",
    "token": "$token",
    "avatar":"$avatar"
}''';
    print("body : $body");
    return ApiManager.instance.makeApiCall(
      callName: 'Login with Line',
      apiUrl: '${domain.domain}${ApiEndPointV2.LOGIN_LINE}',
      callType: ApiCallType.POST,
      headers: {},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic message(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic token(dynamic response) => getJsonField(response, r'''$.data.api_token''');
}

class RegisterCall {
  static Future<ApiCallResponse> call({
    String name = '',
    String username = '',
    String email = '',
    String phone = '',
    String password = '',
    String confirmPassword = '',
  }) {
    final body =
        '''
{
  "name": "$name",
  "username": "$username",
  "email": "$email",
  "phone": "$phone",
  "password": "$password",
  "confirm_password": "$confirmPassword"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Register',
      apiUrl: ApiEndPoint.REGISTER,
      callType: ApiCallType.POST,
      headers: {},
      params: {'name': name, 'username': username, 'email': email, 'phone': phone, 'password': password, 'confirm_password': confirmPassword},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  Future register({
    required String name,
    required String username,
    required String email,
    required String phone,
    required String password,
    required String confirmPassword,
  }) async {
    try {
      final headers = {'Content-Type': 'application/json'};
      final request = http.Request('POST', Uri.parse(ApiEndPoint.REGISTER));
      request.body = json.encode({
        "name": name,
        "username": username,
        "email": email,
        "phone": phone,
        "password": password,
        "confirm_password": confirmPassword,
      });

      request.headers.addAll(headers);

      final http.StreamedResponse response = await request.send();
      print('code : ${response.statusCode}');

      if (response.statusCode == 200) {
        print('on if');
        final responseBody = await http.Response.fromStream(response);
        print('responseBody: ${responseBody.body}');

        final decodedResponse = jsonDecode(responseBody.body);
        print('decoder = $decodedResponse');

        return decodedResponse;
      } else {
        print('Error: ${response.reasonPhrase}');
        return null; // or handle error in a way appropriate for your application
      }
    } catch (e, stackTrace) {
      print('register exception : $e');
      print(stackTrace);
      return null; // or handle error in a way appropriate for your application
    }
  }

  Future registerOtp({required String phone}) async {
    try {
      final headers = {'Content-Type': 'application/json'};
      final request = http.Request('POST', Uri.parse('${domain.domain}${ApiEndPointV2.REGISTER_SMS}'));
      request.body = json.encode({"mobile": phone});
      request.headers.addAll(headers);

      final http.StreamedResponse response = await request.send();
      if (response.statusCode == 200) {
        print('on if');
        final res = await response.stream.bytesToString();
        final json = jsonDecode(res);
        print('decoder = $json');

        return json;
      } else {
        print(response.reasonPhrase);
      }
    } catch (e, stackTrace) {
      debugPrintStack(label: 'Error: $e', stackTrace: stackTrace);
    }
  }

  static dynamic success(dynamic response) => getJsonField(response, r'''$.success''');

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');

  static dynamic message(dynamic response) => getJsonField(response, r'''$.message''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic apitoken(dynamic response) => getJsonField(response, r'''$.data.api_token''');
}

class ResetPasswordCall {
  static Future<ApiCallResponse> call({String mobile = ''}) {
    final body =
        '''
{
  "mobile": "$mobile"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'resetpassword',
      apiUrl: ApiEndPoint.RE_PASSWORD,
      callType: ApiCallType.POST,
      headers: {},
      params: {'mobile': mobile},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic refcode(dynamic response) => getJsonField(response, r'''$.ref_code''');
}

class VerifyOTPResetPasswordCall {
  static Future<CheckVerifyResponseModel> callVerifySms({required String mobile, required String otpCode}) async {
    try {
      final headers = {'Content-Type': 'application/json'};
      final request = http.Request('POST', Uri.parse('${domain.domain}${ApiEndPointV2.VERIFY_RESET_PASSWORD}'));
      final body = {"mobile": mobile, "otpCode": otpCode};
      request.body = json.encode(body);
      request.headers.addAll(headers);

      final http.StreamedResponse response = await request.send();
      if (response.statusCode == 200) {
        final responseString = await response.stream.bytesToString();

        final res = jsonDecode(responseString);
        return CheckVerifyResponseModel.fromJson(res);
      } else {
        return CheckVerifyResponseModel(status: false, code: '', message: 'เกิดข้อผลพลาด ลองใหม่ครั้ง', data: null, onetimeToken: '', user: null);
      }
    } catch (e, stackTrace) {
      debugPrintStack(label: 'Error: $e', stackTrace: stackTrace);
      return CheckVerifyResponseModel(status: false, code: '', message: 'เกิดข้อผลพลาด ลองใหม่ครั้ง', data: null, onetimeToken: '', user: null);
    }
  }

  /*  static Future<ApiCallResponse> call({String mobile = '', String otpCode = ''}) {
    final body =
        '''
{
  "mobile": "$mobile",
  "otpCode": "$otpCode"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Verify OTP Reset Password',
      apiUrl: ApiEndPoint.VERIFY_RESET_PASSWORD,
      callType: ApiCallType.POST,
      headers: {},
      params: {'mobile': mobile, 'otpCode': otpCode},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic resetpasswordtoken(dynamic response) => getJsonField(response, r'''$.reset_password_token''');

  static dynamic apitoken(dynamic response) => getJsonField(response, r'''$.user.api_token''');*/
}

class SetNewPasswordCall {
  static Future<ApiCallResponse> call({String mobile = '', String resetToken = '', String password = ''}) {
    final body =
        '''
{
  "mobile": "$mobile",
  "reset_token": "$resetToken",
  "password": "$password"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Set New Password',
      apiUrl: ApiEndPoint.RESET_NEW_PASSWORD,
      callType: ApiCallType.POST,
      headers: {},
      params: {'mobile': mobile, 'reset_token': resetToken, 'password': password},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');
}

class SlidesCall {
  static Future<ApiCallResponse> call() {
    return ApiManager.instance.makeApiCall(
      callName: 'slides',
      apiUrl: ApiEndPoint.HOME_SLIDES,
      callType: ApiCallType.GET,
      headers: {},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic mainslides(dynamic response) => getJsonField(response, r'''$.data.main_slides''');

  static dynamic promotions(dynamic response) => getJsonField(response, r'''$.data.promotions''');
}

class FlashsalesCall {
  static Future<ApiCallResponse> call() {
    return ApiManager.instance.makeApiCall(
      callName: 'flashsales',
      apiUrl: '${domain.domain}/api/app/home/<USER>',
      callType: ApiCallType.GET,
      headers: {},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic flashsales(dynamic response) => getJsonField(response, r'''$.data.flash_sales''');
}

class ProvinceCall {
  static Future<ApiCallResponse> call() {
    return ApiManager.instance.makeApiCall(
      callName: 'Province',
      apiUrl: 'https://uat-api-seller.shopchill.net/api/thailand/province',
      callType: ApiCallType.GET,
      headers: {},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.success''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic province(dynamic response) => getJsonField(response, r'''$.data.province''');
}

class DistrictCall {
  static Future<ApiCallResponse> call({String? province = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'Province',
      apiUrl: 'https://uat-api-seller.shopchill.net/api/thailand/district',
      callType: ApiCallType.GET,
      headers: {},
      params: {'province': province},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.success''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic district(dynamic response) => getJsonField(response, r'''$.data.district''');
}

class SubDistrictCall {
  static Future<ApiCallResponse> call({String? province = '', String? district = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'Province',
      apiUrl: 'https://uat-api-seller.shopchill.net/api/thailand/sub-district',
      callType: ApiCallType.GET,
      headers: {},
      params: {'province': province, 'district': district},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.success''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic subDistrict(dynamic response) => getJsonField(response, r'''$.data.sub_district''');
}

class ZipcodeCall {
  static Future<ApiCallResponse> call({String? province = '', String? district = '', String? subDistrict = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'Province',
      apiUrl: '${domain.domainSeller}/api/thailand/zipcode',
      callType: ApiCallType.GET,
      headers: {},
      params: {'province': province, 'district': district, 'sub_district': subDistrict},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.success''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic zipcode(dynamic response) => getJsonField(response, r'''$.data.zipcode''');
}

class CategoriesCall {
  static Future<ApiCallResponse> call() {
    return ApiManager.instance.makeApiCall(
      callName: 'categories',
      apiUrl: '${domain.domain}/api/app/home/<USER>',
      callType: ApiCallType.GET,
      headers: {},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic categories(dynamic response) => getJsonField(response, r'''$.data.categories''');
}

class ProductsCall {
  static Future<ApiCallResponse> call({int? limit, int? offset, String? type = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'products',
      apiUrl: '${domain.domain}/api/app/home/<USER>',
      callType: ApiCallType.GET,
      headers: {},
      params: {'limit': limit, 'offset': offset, 'type': type},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic products(dynamic response) => getJsonField(response, r'''$.data.products''');

  static dynamic count(dynamic response) => getJsonField(response, r'''$.data.count''');
}

class ChillMallCall {
  static Future<ApiCallResponse> call() {
    return ApiManager.instance.makeApiCall(
      callName: 'chillmall',
      apiUrl: '${domain.domain}/api/app/home/<USER>',
      callType: ApiCallType.GET,
      headers: {},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic chillmall(dynamic response) => getJsonField(response, r'''$.data.chill_mall''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');
}

class CartCall {
  static Future<ApiCallResponse> call({String? token = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'cart',
      apiUrl: '${domain.domain}/api/app/cart',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic carts(dynamic response) => getJsonField(response, r'''$.data.carts''');
}

class GetProfileCall {
  static Future<ApiCallResponse> call({String? token = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'getProfile',
      apiUrl: '${domain.domain}/api/app/user',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic profile(dynamic response) => getJsonField(response, r'''$.data.profile''');

  static dynamic name(dynamic response) => getJsonField(response, r'''$.data.profile.name''');

  static dynamic username(dynamic response) => getJsonField(response, r'''$.data.profile.username''');

  static dynamic avatar(dynamic response) => getJsonField(response, r'''$.data.profile.avatar''');

  static dynamic email(dynamic response) => getJsonField(response, r'''$.data.profile.email''');

  static dynamic hasPassword(dynamic response) => getJsonField(response, r'''$.data.profile.has_password''');
}

class UpdateProfileCall {
  static Future<ApiCallResponse> call({String? token = '', String? avatar = '', String? name = '', String? email = '', String? username = ''}) {
    final body =
        '''
{
  "avatar": "$avatar",
  "name": "$name",
  "email": "$email",
  "username": "$username"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'update profile',
      apiUrl: '${domain.domain}/api/app/user',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {'avatar': avatar, 'name': name, 'email': email, "username": username},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');
}

class RepassWordCall {
  static Future<ApiCallResponse> call({String? token = '', String? oldPassword = '', String? password = '', String? cfPassword = ''}) {
    final body =
        '''
{
  "old_password": "$oldPassword",
  "password": "$password",
  "cf_password": "$cfPassword"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 're-password',
      apiUrl: '${domain.domain}/api/app/user/re-password',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {'old_password': oldPassword, 'password': password, 'cf_password': cfPassword},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic message(dynamic response) => getJsonField(response, r'''$.message''');
}

class ProductCategoryCall {
  static Future<ApiCallResponse> call({int? limit, int? offset, int? categoryId}) {
    return ApiManager.instance.makeApiCall(
      callName: 'ProducrCategory',
      apiUrl: '${domain.domain}s/api/app/category',
      callType: ApiCallType.GET,
      headers: {},
      params: {'limit': limit, 'offset': offset, 'category_id': categoryId},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic products(dynamic response) => getJsonField(response, r'''$.data.products''');
}

class SearchCall {
  static Future<ApiCallResponse> call({String? keyword = '', int? limit, int? offset}) {
    return ApiManager.instance.makeApiCall(
      callName: 'search',
      apiUrl: '${domain.domain}/api/app/search',
      callType: ApiCallType.GET,
      headers: {},
      params: {'keyword': keyword, 'limit': limit, 'offset': offset},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');

  static dynamic products(dynamic response) => getJsonField(response, r'''$.data.products''');
}

class GetPaymentGatewayCall {
  static Future<ApiCallResponse> call() {
    return ApiManager.instance.makeApiCall(
      callName: 'GetPaymentGateway',
      apiUrl: '${domain.domain}/api/app/get-payment-gateway',
      callType: ApiCallType.GET,
      headers: {},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');
}

class SearchSuggestCall {
  static Future<ApiCallResponse> call({String token = '', String keyword = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'SearchSuggest',
      apiUrl: '${domain.domain}/api/app/search-suggest',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {'keyword': keyword},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic products(dynamic response) => getJsonField(response, r'''$.data.products''');
}

class AllNotificationCall {
  static Future<ApiCallResponse> call({String token = '', int? limit, int? offset}) {
    return ApiManager.instance.makeApiCall(
      callName: 'AllNotification',
      apiUrl: '${domain.domain}/api/app/notification',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {'limit': limit, 'offset': offset},
      returnBody: false,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic notices(dynamic response) => getJsonField(response, r'''$.data.notices''');
}

class AddcartCall {
  static Future<ApiCallResponse> call({String token = '', String shopId = '', String productId = '', int productQty = 0, int productOptionId = 0}) {
    final body =
        '''
{
  "shop_id": "$shopId",
  "product_id": "$productId",
  "product_qty": "$productQty",
  "product_option_id": "$productOptionId"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'addcart',
      apiUrl: '${domain.domain}/api/app/add-cart',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {'shop_id': shopId, 'product_id': productId, 'product_qty': productQty, "product_option_id": productOptionId},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');
}

class GetCartCall {
  static Future<ApiCallResponse> call({String token = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'GetCart',
      apiUrl: '${domain.domain}/api/app/cart',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic carts(dynamic response) => getJsonField(response, r'''$.data.carts''');

  static dynamic cartProducts(dynamic response) => getJsonField(response, r'''$.products''');

  static dynamic countCart(dynamic response) => getJsonField(response, r'''$.data.count_cart''');
}

class UpdateCartCall {
  static Future<ApiCallResponse> call({String cartId = '', int qty = 0, String token = ''}) {
    final body =
        '''
{
  "cart_id": "$cartId",
  "qty": "$qty"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Update Cart',
      apiUrl: '${domain.domain}/api/app/update-cart',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {'cart_id': cartId, 'qty': qty},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');
}

class RemoveCartCall {
  static Future<ApiCallResponse> call({String cartId = '', String token = ''}) {
    final body =
        '''
{
  "cart_id": "$cartId"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Update Cart',
      apiUrl: '${domain.domain}/api/app/remove-cart',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {'cart_id': cartId},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');
}

class MyAddressCall {
  static Future<ApiCallResponse> call({String token = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'MyAddress',
      apiUrl: '${domain.domain}/api/app/user/address',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic address(dynamic response) => getJsonField(response, r'''$.data.address''');
}

class UpdateAddressCall {
  static Future<ApiCallResponse> call({
    String token = '',
    int id = 0,
    String name = '',
    String phone = '',
    String address = '',
    String district = '',
    String amphure = '',
    String province = '',
    String zipcode = '',
    int isPrimary = 0,
  }) {
    final body = id == 0
        ? '''
{
    "name": "$name",
    "phone": "$phone",
    "address": "$address",
    "district": "$district",
    "amphure": "$amphure",
    "province": "$province",
    "zipcode": "$zipcode",
    "is_primary": $isPrimary
}'''
        : '''
{
    "id": $id,
    "name": "$name",
    "phone": "$phone",
    "address": "$address",
    "district": "$district",
    "amphure": "$amphure",
    "province": "$province",
    "zipcode": "$zipcode",
    "is_primary": $isPrimary
}''';

    return ApiManager.instance.makeApiCall(
      callName: 'Update Address',
      apiUrl: '${domain.domain}/api/app/user/address',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');
}

class CalculateCartCall {
  static Future<ApiCallResponse> call({String token = '', List? carts, List? shopCoupons, String coupon = ''}) {
    final body =
        '''
{
    "carts": $carts,
    "shop_coupons":  $shopCoupons,
    "coupon": "$coupon"
}''';

    return ApiManager.instance.makeApiCall(
      callName: 'Calculate Cart',
      apiUrl: '${domain.domain}/api/app/calculate-cart',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {'carts': carts, 'shop_coupons': shopCoupons, 'coupon': coupon},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');
}

class MyCouponCall {
  static Future<ApiCallResponse> call({String token = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'MyCoupon',
      apiUrl: '${domain.domain}/api/app/user/coupon',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic coupons(dynamic response) => getJsonField(response, r'''$.data.coupons''');
}

class CouponShippingFreeCall {
  static Future<ApiCallResponse> call({String token = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'Coupon Shipping Free',
      apiUrl: '${domain.domain}/api/app/home/<USER>',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic shopchillShippingCoupons(dynamic response) => getJsonField(response, r'''$.shopchill_shipping_coupons''');

  static dynamic shopchillMonthCoupons(dynamic response) => getJsonField(response, r'''$.shopchill_month_coupons''');
}

class PaymentCall {
  static Future<ApiCallResponse> call({String token = '', int? masterOrderId = 0}) {
    final body =
        '''
{
    "master_order_id": $masterOrderId
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Payment',
      apiUrl: '${domain.domain}/api/app/payment',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');
}

class CheckOutCall {
  static Future<ApiCallResponse> call({String token = "", final body}) {
    return ApiManager.instance.makeApiCall(
      callName: 'Checkout',
      apiUrl: '${domain.domain}/api/app/checkout',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');

  static dynamic orders(dynamic response) => getJsonField(response, r'''$.data.orders''');
}

class SaveCouponCall {
  static Future<ApiCallResponse> call({String token = '', int? couponId = 0}) {
    final body =
        '''
{
    "coupon_id": $couponId
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Save coupon',
      apiUrl: '${domain.domain}/api/app/save-coupon',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');
}

class MyOrderCall {
  static Future<ApiCallResponse> call({String token = "", int? limit, int? offset, int? status}) {
    return ApiManager.instance.makeApiCall(
      callName: 'MyOrder',
      apiUrl: '${domain.domain}/api/app/user/order',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {'limit': limit, 'offset': offset, 'status': status},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic orders(dynamic response) => getJsonField(response, r'''$.data.orders''');

  static dynamic countAllOrders(dynamic response) => getJsonField(response, r'''$.data.count_all_orders''');
}

/*class OrderDetailCall {
  static Future<ApiCallResponse> call({
    String token = "",
    int? id,
  }) {
    return ApiManager.instance.makeApiCall(
      callName: 'Order Detail',
      apiUrl: '${domain.domain}/api/app/user/order/$id',
      callType: ApiCallType.GET,
      headers: {
        'Authorization': 'Bearer $token',
      },
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(
        response,
        r'''$.status''',
      );

  static dynamic msg(dynamic response) => getJsonField(
        response,
        r'''$.msg''',
      );

  static dynamic masterOrder(dynamic response) => getJsonField(
        response,
        r'''$.data.master_order''',
      );

  static dynamic orders(dynamic response) => getJsonField(
        response,
        r'''$.data.master_order.orders''',
      );
}*/

class CheckoutSuccessCall {
  static Future<ApiCallResponse> call({String token = "", int? mt}) {
    return ApiManager.instance.makeApiCall(
      callName: 'MyOrder',
      apiUrl: '${domain.domain}/checkout-success',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {'mt': mt},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic orders(dynamic response) => getJsonField(response, r'''$.data.orders''');
}

class PaymentNotifyCall {
  static Future<ApiCallResponse> call({String token = '', int? paymentId = 0, String slip = '', String dateTranfer = '', String timeTranfer = ''}) {
    final body =
        '''
{
    "payment_id": $paymentId,
    "slip": "$slip",
    "date_tranfer": "$dateTranfer",
    "time_tranfer": "$timeTranfer"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Payment Notify',
      apiUrl: '${domain.domain}/api/payment/notify',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.success''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');
}

class UploadMediaCall {
  static Future<ApiCallResponse> call({String token = '', String? file = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'Upload Media',
      apiUrl: '${domain.domain}/api/app/upload',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {'file': file},
      bodyType: BodyType.FORM_DATA,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');
}

class CancelOrderCall {
  static Future<ApiCallResponse> call({String token = '', int? masterOrderId = 0, int? cancelReason = 0}) {
    final body =
        '''
{
    "master_order_id": $masterOrderId,
     "cancel_reason": $cancelReason
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Cancel Order',
      apiUrl: '${domain.domain}/api/app/user/cancel-order',
      callType: ApiCallType.POST,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      body: body,
      bodyType: BodyType.JSON,
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic data(dynamic response) => getJsonField(response, r'''$.data''');
}

class CancelOrderReasonsCall {
  static Future<ApiCallResponse> call({String token = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'Cancel Order Reasons',
      apiUrl: '${domain.domain}/api/app/user/cancel-order-reasons',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic reasons(dynamic response) => getJsonField(response, r'''$.data.reasons''');
}

class TrackingOrderCall {
  static Future<ApiCallResponse> call({String token = '', String track = ''}) {
    return ApiManager.instance.makeApiCall(
      callName: 'Tracking Order',
      apiUrl: '${domain.domain}/api/app/tracking',
      callType: ApiCallType.GET,
      headers: {'Authorization': 'Bearer $token'},
      params: {'track': track},
      returnBody: true,
    );
  }

  static dynamic status(dynamic response) => getJsonField(response, r'''$.status''');

  static dynamic msg(dynamic response) => getJsonField(response, r'''$.msg''');

  static dynamic log(dynamic response) => getJsonField(response, r'''$.data.log''');
}

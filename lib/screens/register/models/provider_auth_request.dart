class ProviderAuthRequest {
  final String id;
  final String name;
  final String email;
  final String token;
  final String? avatar;
  final String fcmToken;

  const ProviderAuthRequest({required this.id, required this.name, required this.email, required this.token, this.avatar, required this.fcmToken});

  factory ProviderAuthRequest.fromJson(Map<String, dynamic> json) {
    return ProviderAuthRequest(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      token: json['token'] ?? '',
      avatar: json['avatar'] ?? '',
      fcmToken: json['fcm_token'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {'id': id, 'name': name, 'email': email, 'token': token, 'avatar': avatar, 'fcm_token': fcmToken};
}

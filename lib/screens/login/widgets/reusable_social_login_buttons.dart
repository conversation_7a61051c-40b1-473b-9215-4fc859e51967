import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:shop_chill_app/auth/apple_auth.dart';
import 'package:shop_chill_app/auth/facebook_auth.dart';
import 'package:shop_chill_app/auth/google_auth.dart';
import 'package:shop_chill_app/auth/line_auth.dart';
import 'package:shop_chill_app/screens/login/widgets/social_login_buttons.dart';
import 'package:shop_chill_app/screens/register/services/social_auth_service.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';

class ReusableSocialLoginButtons extends StatefulWidget {
  const ReusableSocialLoginButtons({super.key});

  @override
  State<ReusableSocialLoginButtons> createState() => _ReusableSocialLoginButtonsState();
}

final _authService = SocialAuthService();

class _ReusableSocialLoginButtonsState extends State<ReusableSocialLoginButtons> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        SocialLoginButtons(
          svgIconPath: UImageAssets.FACEBOOK_SVG,
          onTap: () async {
            final user = await signInWithFacebook(context);
            if (user != null) {
              _authService.authenticate(context, user: user, provider: ProviderType.facebook);
            }
          },
        ),
        SocialLoginButtons(
          svgIconPath: UImageAssets.LINE_SVG,
          onTap: () async {
            final userProfile = await signInWithLine(context);
            if (userProfile != null) {
              _authService.authenticate(context, lineUserProfile: userProfile, provider: ProviderType.line);
            }
          },
        ),
        SocialLoginButtons(
          svgIconPath: UImageAssets.GOOGLE_SVG,
          onTap: () async {
            final user = await signInWithGoogle(context);
            if (user != null) {
              _authService.authenticate(context, user: user, provider: ProviderType.google);
            }
          },
        ),

        if (Platform.isIOS)
          SocialLoginButtons(
            svgIconPath: UImageAssets.APPLE_SVG,
            onTap: () async {
              final user = await signInWithApple(context);
              if (user != null) {
                _authService.authenticate(context, user: user, provider: ProviderType.apple);
              }
            },
          ),
      ],
    );
  }
}

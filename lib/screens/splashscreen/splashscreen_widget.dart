import 'package:shop_chill_app/shered/assets/image_assets.dart';

import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import '../flutter_flow/flutter_flow_widgets.dart';
import '../login/login_widget.dart';
import '../register/register_paget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

class SplashscreenWidget extends StatefulWidget {
  const SplashscreenWidget({super.key});

  @override
  _SplashscreenWidgetState createState() => _SplashscreenWidgetState();
}

class _SplashscreenWidgetState extends State<SplashscreenWidget> {
  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Stack(
                  children: [
                    Image.asset(
                      // 'assets/images/0_SplashScreen.png',
                      UImageAssets.SPLASH_SCREEN,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 90),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(0, 0),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 16),
                              child: FFButtonWidget(
                                onPressed: () async {
                                  await Navigator.push(
                                    context,
                                    PageTransition(
                                      type: PageTransitionType.rightToLeft,
                                      duration: const Duration(milliseconds: 0),
                                      reverseDuration: const Duration(milliseconds: 0),
                                      child: const RegisterPage(),
                                    ),
                                  );
                                },
                                text: 'สมัครสมาชิก',
                                options: FFButtonOptions(
                                  width: 303,
                                  height: 60,
                                  color: Colors.white,
                                  textStyle: FlutterFlowTheme.of(context).title1.override(
                                    fontFamily: 'Sarabun',
                                    color: FlutterFlowTheme.of(context).primaryColor,
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                                  borderSide: const BorderSide(color: Colors.transparent, width: 1),
                                  borderRadius: BorderRadius.circular(14),
                                ),
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () async {
                              await Navigator.push(
                                context,
                                PageTransition(
                                  type: PageTransitionType.rightToLeft,
                                  duration: const Duration(milliseconds: 0),
                                  reverseDuration: const Duration(milliseconds: 0),
                                  child: const LoginWidget(),
                                ),
                              );
                            },
                            child: Text(
                              'เข้าสู่ระบบ',
                              style: FlutterFlowTheme.of(
                                context,
                              ).title1.override(fontFamily: 'Sarabun', color: Colors.white, fontWeight: FontWeight.w600, useGoogleFonts: false),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum LoginFromWhere { google, facebook, apple, line, others }

extension LoginFromWhereExtension on LoginFromWhere {
  String get name => toString().replaceAll(RegExp(r".*\."), "");
}

class CheckLoginFromWhere {
  static LoginFromWhere checkLoginFromWhere(String providerId) {
    if (providerId == 'google.com') {
      return LoginFromWhere.google;
    } else if (providerId == 'facebook.com') {
      return LoginFromWhere.facebook;
    } else if (providerId == 'apple.com') {
      return LoginFromWhere.apple;
    } else if (providerId == 'line.com') {
      return LoginFromWhere.line;
    } else {
      return LoginFromWhere.others;
    }
  }

  static String loginFromWhereName(LoginFromWhere loginFromWhere) {
    switch (loginFromWhere) {
      case LoginFromWhere.google:
        return 'Google';
      case LoginFromWhere.facebook:
        return 'Facebook';
      case LoginFromWhere.apple:
        return 'Apple';
      case LoginFromWhere.line:
        return 'Line';
      case LoginFromWhere.others:
        return '';
    }
  }
}

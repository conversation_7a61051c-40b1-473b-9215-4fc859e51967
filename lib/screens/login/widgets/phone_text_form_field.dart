import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';

class PhoneTextFormField extends StatelessWidget {
  final TextEditingController phoneController;
  final bool havePrefixIcon;
  final String labelText;
  final String hintText;
  const PhoneTextFormField({
    super.key,
    required this.phoneController,
    this.havePrefixIcon = true,
    this.labelText = 'หมายเลขโทรศัพท์',
    this.hintText = 'ระบุหมายเลขโทรศัพท์ 10 หลัก',
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: phoneController,
      builder: (context, value, _) {
        final length = value.text.length;
        return TextFormField(
          maxLength: 10,
          controller: phoneController,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          obscureText: false,
          decoration: InputDecoration(
            counterText: '',
            labelText: labelText,
            hintText: hintText,
            labelStyle: Theme.of(context).textTheme.labelMedium?.copyWith(color: ColorThemeConfig.primaryColor, fontWeight: FontWeight.w700),
            prefixIcon: havePrefixIcon ? const Icon(Icons.phone, size: 24, color: ColorThemeConfig.primaryColor) : null,
            hintStyle: Theme.of(context).textTheme.bodySmall?.copyWith(color: ColorThemeConfig.unfocusedTextColor),
            enabledBorder: OutlineInputBorder(
              borderSide: havePrefixIcon ? BorderSide.none : const BorderSide(color: ColorThemeConfig.primaryColor, width: 1),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: ColorThemeConfig.primaryColor, width: 1),
              borderRadius: BorderRadius.circular(8),
            ),
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            filled: true,
            suffixText: '$length/10',
            suffixStyle: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey),
            fillColor: havePrefixIcon ? ColorThemeConfig.fillColor : Colors.white,
            contentPadding: const EdgeInsets.all(10),
          ),

          style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black),
        );
      },
    );
  }
}

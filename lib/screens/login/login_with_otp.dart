import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/login/cubit/timer_cubit.dart';
import 'package:shop_chill_app/screens/login/cubit/timer_state.dart';
import 'package:shop_chill_app/screens/login/otp_confirmation.dart';
import 'package:shop_chill_app/screens/login/widgets/phone_text_form_field.dart';
import 'package:shop_chill_app/screens/login/widgets/submit_button.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_bg.dart';
import 'package:shop_chill_app/shered/widgets/shopchill_logo.dart';

import '../../config/shopchill_loading/shopchill_loading.dart';

class LoginWithOTP extends StatefulWidget {
  const LoginWithOTP({super.key});

  @override
  State<LoginWithOTP> createState() => _LoginWithOTPState();
}

class _LoginWithOTPState extends State<LoginWithOTP> {
  TextEditingController mobileController = TextEditingController();

  final ValueNotifier<bool> isEnabledValue = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    mobileController.addListener(_validateInput);
  }

  void _validateInput() {
    final isCountdown = context.read<WorkoutCubit>().state is WorkoutInProgress;
    final isValid = mobileController.text.isNotEmpty && mobileController.text.length == 10 && !isCountdown;
    isEnabledValue.value = isValid;
  }

  @override
  void dispose() {
    mobileController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: _buildAppBar(context),
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,

        body: ShopchillBg(
          child: Column(
            children: [
              const ShopchillLogo(isHorizontal: true),
              20.gap,
              _buildInputPhoneNumberSection(context),
              const Spacer(),
              _buildSendOTPButtonSection(),
            ],
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () => Navigator.pop(context, false),
        icon: const Icon(Icons.arrow_back_ios, color: ColorThemeConfig.primaryColor, size: 20),
      ),
      title: Text('เข้าสู่ระบบด้วย OTP', style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black)),

      centerTitle: true,
      elevation: 0,
    );
  }

  Widget _buildInputPhoneNumberSection(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('หมายเลขโทรศัพท์', style: FlutterFlowTheme.of(context).bodyText1.copyWith(color: Colors.black)),
        10.gap,
        PhoneTextFormField(phoneController: mobileController, havePrefixIcon: false),
      ],
    );
  }

  Widget _buildSendOTPButtonSection() {
    return BlocConsumer<WorkoutCubit, WorkoutState>(
      listener: (context, state) {
        _validateInput();
      },
      builder: (context, state) {
        return SubmitButton(
          isEnabled: isEnabledValue,
          onTap: () async => _loginWhithOTPProcess(),
          label: state is WorkoutInProgress ? 'ส่งได้อีกครั้งในอีก ${state.elapsed}' : 'ขอรหัส OTP',
        );
      },
    );
  }

  void _loginWhithOTPProcess() {
    if (mobileController.text.isEmpty) {
      ShopChillLoading.showError("กรุณาระบุหมายเลขโทรศัพท์");

      return;
    }
    showLoading();
    try {
      RegisterCall().registerOtp(phone: mobileController.text).then((value) {
        hideLoading();
        if (value['status'] == true) {
          BlocProvider.of<WorkoutCubit>(context).startWorkout(0);
          Navigator.pushReplacement(
            context,
            CupertinoPageRoute(
              builder: (context) =>
                  OtpConfirmation(mobile: mobileController.text, refCode: value['ref_code'], type: ConfirmationOtpType.loginWhithOTP),
            ),
          );
        } else {
          ShopChillLoading.showError(value['message'] ?? value['msg']);
        }
      });
    } catch (e, stackTrace) {
      debugPrintStack(label: 'Error: $e', stackTrace: stackTrace);
    } finally {
      hideLoading();
    }
  }
}

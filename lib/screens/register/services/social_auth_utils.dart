import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/backend/schema/local_database/local_database.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/register/models/provider_auth_response.dart';
import 'package:shop_chill_app/shered/util/check_login_from_where.dart';
import 'package:shop_chill_app/shered/util/dialog.dart';

class SocialAuthUtils {
  static final SocialAuthUtils _instance = SocialAuthUtils._internal();
  factory SocialAuthUtils() => _instance;
  SocialAuthUtils._internal();

  Future<ProviderAuthResponse?> checkOrReActivateAndLogin<T>({
    required BuildContext context,
    required String? uid,
    required Future<List<T>> Function() getLocalProfiles,
    required Future<ProviderAuthResponse?> Function() callLogin,
    required Future<void> Function(String? uid) deleteProfileByUid,
  }) async {
    final isExist = (await getLocalProfiles()).any((e) {
      if (e is FacebookProfile) return e.uid == uid;
      if (e is GoogleProfile) return e.uid == uid;
      if (e is AppleProfile) return e.uid == uid;
      if (e is LineProfile) return e.uid == uid;
      return false;
    });

    if (isExist) {
      final completer = Completer<ProviderAuthResponse>();
      ShopChillDialogs().showReactivationAccountDialog(
        context,
        callback: () async {
          await deleteProfileByUid(uid);
          final response = await callLogin();
          completer.complete(response);
        },
      );
      return completer.future;
    } else {
      return callLogin();
    }
  }

  bool getStatusFromResponse(ProviderType provider, ApiCallResponse response) {
    switch (provider) {
      case ProviderType.facebook:
        return LoginWithFacebookCall.status(response.jsonBody ?? '');
      case ProviderType.google:
        return LoginWithGoogleCall.status(response.jsonBody ?? '');
      case ProviderType.apple:
        return LoginWithAppleCall.status(response.jsonBody ?? '');
      case ProviderType.line:
        return LoginWithLineCall.status(response.jsonBody ?? '');
      case ProviderType.others:
        return false;
    }
  }

  String? getTokenFromResponse(ProviderType provider, ApiCallResponse response) {
    switch (provider) {
      case ProviderType.facebook:
        return LoginWithFacebookCall.token(response.jsonBody ?? '').toString();
      case ProviderType.google:
        return LoginWithGoogleCall.token(response.jsonBody ?? '').toString();
      case ProviderType.apple:
        return LoginWithAppleCall.token(response.jsonBody ?? '')?.toString();
      case ProviderType.line:
        return LoginWithLineCall.token(response.jsonBody ?? '')?.toString();
      case ProviderType.others:
        return null;
    }
  }

  void handleError(BuildContext context, ProviderType provider, ApiCallResponse response) {
    switch (provider) {
      case ProviderType.facebook:
        showError(LoginWithFacebookCall.message(response.jsonBody ?? ''));
        break;
      case ProviderType.google:
        showError(LoginWithGoogleCall.message(response.jsonBody ?? ''));
        break;
      case ProviderType.apple:
        showError(LoginWithAppleCall.message(response.jsonBody ?? ''));
        break;
      case ProviderType.line:
        showError(LoginWithLineCall.message(response.jsonBody ?? ''));
        break;
      case ProviderType.others:
        break;
    }
  }
}
